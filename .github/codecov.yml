coverage:
  # Display coverage percentage with 1 decimal point (XX.X%).
  precision: 1
  round: down

  # FIXME: https://github.com/Kong/kubernetes-ingress-controller/issues/1552
  # Ensure that the Codecov check is green regardless of the coverage number.
  # This is because our current coverage pipeline is unstable and is
  # causing fluctuations in coverage numbers being reported across runs.
  # Also @m<PERSON><PERSON><PERSON> and @shaneutt agree that this is not a 100% effective way
  # to communicate coverage problem on a PR.
  range: "0..100"
  status:
    project:
      default:
        # Do not use coverage drop as a reason to mark the CI check red. Same reason
        # as above.
        threshold: "100%"
    # Do not fail Github checks for patches with low coverage: treat them as
    # informational.
    # https://docs.codecov.com/docs/common-recipe-list#set-non-blocking-status-checks
    patch:
      default:
        informational: true

  ignore:
  # Autogenerated code as scaffolded by kubebuilder.
  - "pkg/apis/**"
  - "pkg/clientset/**"

# Disable codecov annotations in Github files changed tab in PRs.
# https://docs.codecov.com/docs/github-checks#disabling-github-checks-patch-annotations-via-yaml
github_checks:
  annotations: false
