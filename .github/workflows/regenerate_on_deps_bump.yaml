# This workflow will regenerate code and manifests, commit&push them on a PR labeled with `renovate/auto-regenerate`.
# It's to make sure that Renovate-created PRs that update dependencies that might affect generated code/manifests,
# will have them regenerated automatically.
name: Regenerate on deps bump

on:
  pull_request:
    types:
      - labeled

permissions:
  contents: read

jobs:
  regenerate:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    if: contains(github.event.*.labels.*.name, 'renovate/auto-regenerate')
    runs-on: ubuntu-latest
    env:
      REF: ${{ github.event.pull_request.head.ref }}
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ env.REF }}
          token: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - name: go mod tidy
        run: go mod tidy

      - name: regenerate
        run: make generate manifests

      - name: commit and push (if changes detected)
        run: |
          git config --global user.name "github-actions"
          git config --global user.email "<EMAIL>"
          git add go.mod go.sum ./test/e2e/manifests ./config/crd
          git status
          git diff-index --quiet HEAD || \
          git commit -m "chore: regenerate" && \
          git push origin ${{ env.REF }}
