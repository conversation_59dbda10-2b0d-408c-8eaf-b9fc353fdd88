name: Generate Kubernetes Gateway API conformance tests report
run-name: "Generate Kubernetes Gateway API conformance tests report ${{ format('ref:{0}', github.event.inputs.tag) }}"

on:
  workflow_dispatch:
    inputs:
      tag:
        description: The version of code to checkout (e.g. v1.2.3 or commit hash)
        required: false
        default: main

permissions:
  contents: read

jobs:
  dependencies-versions:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    outputs:
      helm-kong: ${{ steps.set-versions.outputs.helm-kong }}
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 1
          fetch-tags: true
          ref: ${{ github.event.inputs.tag }}

      - id: set-versions
        name: Set versions
        run: |
          echo "helm-kong=$(yq -ojson -r '.integration.helm.kong' < .github/test_dependencies.yaml )" >> $GITHUB_OUTPUT

  generate-report:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    needs: dependencies-versions
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 1
          fetch-tags: true
          ref: ${{ github.event.inputs.tag }}

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - name: Run conformance tests
        env:
          TEST_KONG_HELM_CHART_VERSION: ${{ needs.dependencies-versions.outputs.helm-kong }}
          TEST_KONG_ROUTER_FLAVOR: expressions
        run: make test.conformance

      # Generated report should be submitted to
      # https://github.com/kubernetes-sigs/gateway-api/tree/main/conformance/reports
      # in future when experimental becomes stable autamate creating PR (add to release workflow).
      # See: https://github.com/Kong/kubernetes-ingress-controller/issues/4654
      - name: Collect conformance tests report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: kong-kubernetes-ingress-controller.yaml
          path: kong-kubernetes-ingress-controller.yaml
