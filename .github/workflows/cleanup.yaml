name: cleanup

on:
  schedule:
    - cron: '*/30 * * * *'
  workflow_dispatch: {}

permissions:
  contents: read

jobs:
  gcloud:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    environment: gcloud
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod
      - name: cleanup orphaned test clusters
        run: go run ./hack/cleanup gke
        env:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
          GOOGLE_PROJECT: ${{ secrets.GOOGLE_PROJECT }}
          GOOGLE_LOCATION: ${{ secrets.GOOGLE_LOCATION }}

  konnect:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod
      - name: cleanup orphaned test clusters
        run: go run ./hack/cleanup konnect
        env:
          # Ref: https://github.com/Kong/sdk-konnect-go/issues/20
          KONG_CUSTOM_DOMAIN: konghq.tech
          # NOTE: This token has to align with the domain above until the linked
          # issue is resolved.
          TEST_KONG_KONNECT_ACCESS_TOKEN: ${{ secrets.K8S_TEAM_KONNECT_ACCESS_TOKEN }}
