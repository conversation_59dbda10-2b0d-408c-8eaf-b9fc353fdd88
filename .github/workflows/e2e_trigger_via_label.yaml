name: trigger e2e (targeted) on label

on:
  pull_request:
    types:
    - labeled

permissions:
  contents: read

jobs:
  trigger-e2e-tests-targeted:
    timeout-minutes: ${{ fromJSON(vars.GHA_EXTENDED_TIMEOUT_MINUTES) }}
    runs-on: ubuntu-latest
    if: contains(github.event.*.labels.*.name, 'ci/run-e2e')
    env:
      GH_TOKEN: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}
      WORKFLOW: .github/workflows/e2e_targeted.yaml
      BRANCH: ${{ github.event.pull_request.head.ref }}
      PR_NUMBER: ${{ github.event.pull_request.number }}
    permissions:
      contents: read
      actions: write
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      # Do not run e2e tests on GKE-based clusters for specific PR, because currently
      # there is no way to use an image built from PR's code for those tests.
      # https://github.com/Kong/kubernetes-testing-framework/issues/587
      - run: |
          gh workflow run ${WORKFLOW} --ref ${BRANCH} \
            -f run-gke=false \
            -f run-istio=true \
            -f all-supported-k8s-versions=true \
            -f pr-number=${PR_NUMBER}
