name: <PERSON><PERSON> validate Kong image test (targeted) via issue label
on:
  issues:
    types:
    - labeled

permissions:
  contents: read

jobs:
  # Firstly remove the issue label to avoid duplicate triggers.
  check-and-remove-issue-label:
    name: Check label "ci/run-validate-kong-image" and remove the label
    if: contains(github.event.*.labels.*.name, 'ci/run-validate-kong-image')
    runs-on: ubuntu-latest
    permissions:
      contents: read
      issues: write
    env:
      ISSUE_NUMBER: ${{ github.event.issue.number }}
      GH_TOKEN: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: "Remove the label ci/run-validate-kong-image if exist"
        run: gh issue edit ${ISSUE_NUMBER} --remove-label ci/run-validate-kong-image

  extract-and-parse-container-image:
    name: Extract container image repo and tag from issue body
    needs:
    - check-and-remove-issue-label
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    outputs:
      kong_image_repo: ${{ steps.extract_repo_tag.outputs.TEST_KONG_IMAGE_REPO }}
      kong_image_tag: ${{ steps.extract_repo_tag.outputs.TEST_KONG_IMAGE_TAG }}
      kong_image_version: ${{ steps.extract_repo_tag.outputs.TEST_KONG_IMAGE_VERSION }}
      kong_image_major_version: ${{ steps.semver_parser.outputs.major }}
      kong_image_minor_version: ${{ steps.semver_parser.outputs.minor }}
    steps:
      # The body of testing request issue created from the template has the following format:

      # ### What component needs testing with KIC?
      # (empty line)
      #  Kong Gateway EE
      # (empty line)
      # ### Container image
      # (empty line)
      # kong/kong-gateway-dev:2.8.4.8-rc2

      # So we need to extract the line on the position 2 lines after the heading "### Container image".
      
      - name: Extract container image from issue body
        id: extract_image
        env:
          BODY: ${{ github.event.issue.body }}
        # Fail the job if we cannot get "### Container image" keyword from issue body.
        run: |
          kong_container_image=$(echo ${{ env.BODY }} | grep -A 2 '### Container image' | tail -n 1) && \
          echo "TEST_KONG_CONTAINER_IMAGE=${kong_container_image}" >> $GITHUB_ENV
      
      # Split the container into repo and tag by ":" and then extract the first 3 segments of tags as its version.
      # For example, version extracted from tag "3.6.1.2-rc1" is "3.6.1".
      - name: Extract image repo and tag
        id: extract_repo_tag
        run: |
          kong_image_repo=$(echo ${TEST_KONG_CONTAINER_IMAGE} | awk -F':' '{print $1}')
          # Limit kong image repo in [kong,kong/kong-gateway,kong/kong-gateway-dev] to prevent from attack of running unknown images
          if [ "$kong_image_repo" != "kong" ] && [  "$kong_image_repo" != "kong/kong-gateway" ] && [ "$kong_image_repo" != "kong/kong-gateway-dev" ]; then
            echo "invalid image repo: $kong_image_repo"
            exit 1
          fi
          echo "TEST_KONG_IMAGE_REPO=$kong_image_repo" >> $GITHUB_OUTPUT
          kong_image_tag=$(echo ${TEST_KONG_CONTAINER_IMAGE} | awk -F':' '{print $2}')
          echo "TEST_KONG_IMAGE_TAG=${kong_image_tag}" >> $GITHUB_OUTPUT
          kong_image_version=$(echo ${kong_image_tag} | awk -F'.' '{printf("%s.%s.%s",$1,$2,$3)}')
          echo "TEST_KONG_IMAGE_VERSION=${kong_image_version}" >> $GITHUB_ENV 
          echo "TEST_KONG_IMAGE_VERSION=${kong_image_version}" >> $GITHUB_OUTPUT
      
      # We parse the semver from Kong image tag because we need to run with different KIC images with Kong 2.8.x and Kong 3.x.
      - name: Parse Semver of Kong from image tag
        id: semver_parser
        uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
        with:
          input_string: ${{ env.TEST_KONG_IMAGE_VERSION }}
  
  # Choose KIC image and branch to run tests with Kong image by the major and minor version of Kong image.
  # For Kong 2.x (2.8 LTS only) and 3.0-3.3, we use KIC 2.12; otherwise use latest version of KIC.
  decide-kic-branch-and-image:
    name: Decide KIC branch and version to run against the tested Kong image
    needs:
    - extract-and-parse-container-image
    runs-on: ubuntu-latest
    env:
      TEST_KONG_IMAGE_MAJOR_VERSION: ${{ needs.extract-and-parse-container-image.outputs.kong_image_major_version }}
      TEST_KONG_IMAGE_MINOR_VERSION: ${{ needs.extract-and-parse-container-image.outputs.kong_image_minor_version }}
    outputs:
      kic_branch: ${{ steps.choose_kic_branch.outputs.KIC_BRANCH }}
      kic_image_tag: ${{ steps.choose_kic_branch.outputs.KIC_IMAGE_TAG }}
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Choose KIC image and branch
        id: choose_kic_branch
        run: |
          if [[ $TEST_KONG_IMAGE_MAJOR_VERSION -le 2 ]]; then
            echo "KIC_BRANCH=release/2.12.x" >> $GITHUB_OUTPUT
            echo "KIC_IMAGE_TAG=2.12" >> $GITHUB_OUTPUT
          elif [ $TEST_KONG_IMAGE_MAJOR_VERSION -eq 3 ] && [ $TEST_KONG_IMAGE_MINOR_VERSION -le 3 ]; then
            echo "KIC_BRANCH=release/2.12.x" >> $GITHUB_OUTPUT
            echo "KIC_IMAGE_TAG=2.12" >> $GITHUB_OUTPUT
          else
            echo "KIC_BRANCH=main" >> $GITHUB_OUTPUT
            echo "KIC_IMAGE_TAG=latest" >> $GITHUB_OUTPUT
          fi

  run-validate-kong-image:
    name: Run tests to validate Kong image
    needs:
    - check-and-remove-issue-label
    - extract-and-parse-container-image
    - decide-kic-branch-and-image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      actions: write
    env:
      GH_TOKEN: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}
      WORKFLOW: .github/workflows/validate_kong_image.yaml
      ISSUE_NUMBER: ${{ github.event.issue.number }}
      TEST_KONG_IMAGE_REPO: ${{ needs.extract-and-parse-container-image.outputs.kong_image_repo }}
      TEST_KONG_IMAGE_TAG: ${{ needs.extract-and-parse-container-image.outputs.kong_image_tag }}
      TEST_KONG_IMAGE_VERSION: ${{ needs.extract-and-parse-container-image.outputs.kong_image_version }}
      KIC_BRANCH: ${{ needs.decide-kic-branch-and-image.outputs.kic_branch }}
      KIC_IMAGE_TAG: ${{ needs.decide-kic-branch-and-image.outputs.kic_image_tag }} 
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Run validate Kong image tests
        id: run_validate_kong_image
        run: |
          gh workflow run ${WORKFLOW} --ref ${KIC_BRANCH} \
            -f issue_number=${ISSUE_NUMBER} \
            -f kong_image_repo=${TEST_KONG_IMAGE_REPO} \
            -f kong_image_tag=${TEST_KONG_IMAGE_TAG} \
            -f kong_effective_version=${TEST_KONG_IMAGE_VERSION} \
            -f e2e_controller_image_tag=${KIC_IMAGE_TAG}
