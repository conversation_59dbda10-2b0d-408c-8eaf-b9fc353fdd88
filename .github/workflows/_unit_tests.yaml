name: unit tests

on:
  workflow_call: {}

permissions:
  contents: read

jobs:
  unit-tests:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - name: run unit tests
        run: make test.unit
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
          GOTESTSUM_JUNITFILE: unit-tests.xml

      - name: collect test coverage
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: coverage-unit
          path: coverage.unit.out

      - name: collect test report
        if: ${{ always() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: tests-report-unit
          path: unit-tests.xml
