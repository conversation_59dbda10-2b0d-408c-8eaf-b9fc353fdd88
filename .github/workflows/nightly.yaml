name: nightly

on:
  schedule:
    - cron: '30 3 * * *'
  workflow_dispatch: {}

permissions:
  contents: read

jobs:
  build-push-images:
    timeout-minutes: ${{ fromJSON(vars.GHA_EXTENDED_TIMEOUT_MINUTES) }}
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      # Setup Golang to use go pkg cache which is utilized in Dockerfile's cache mount.
      - name: Setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod
      - run: echo "GOPATH=$(go env GOPATH)" >> $GITHUB_ENV
      - run: echo "GOCACHE=$(go env GOCACHE)" >> $GITHUB_ENV

      - name: Add standard tags
        id: tags-standard
        run: |
          (
            echo 'TAGS_STANDARD<<EOF'
            echo 'type=raw,value=nightly'
            echo "type=raw,value={{date 'YYYY-MM-DD'}}"
            echo 'EOF'
          ) >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0
      - name: Cache Docker layers
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        with:
          images: kong/nightly-ingress-controller
          tags: ${{ steps.tags-standard.outputs.TAGS_STANDARD }}
      - name: Build binary
        id: docker_build_binary
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          push: false
          file: Dockerfile
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
          target: builder
          platforms: linux/amd64, linux/arm64
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git
            GOPATH=${{ env.GOPATH}}
            GOCACHE=${{ env.GOCACHE}}
      - name: Login to DockerHub
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_NIGHTLY_PUSH_TOKEN }}
      - name: Build and push distroless image to DockerHub
        id: docker_build
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          push: true
          file: Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=local,src=/tmp/.buildx-cache
          target: distroless
          platforms: linux/amd64, linux/arm64
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git
            GOPATH=${{ env.GOPATH}}
            GOCACHE=${{ env.GOCACHE}}
