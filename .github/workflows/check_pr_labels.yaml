name: PRs labels check

on:
  pull_request:
    types: [opened, reopened, ready_for_review, labeled, unlabeled, synchronize]

permissions:
  contents: read

jobs:
  label:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    steps:
      - uses: pmalek/verify-pr-label-action@7c5cdb8db3e959d689b7f13da21826ec8c9f6f8f #  v1.4.5
        with:
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          invalid-labels: 'do not merge,on-hold'
          pull-request-number: '${{ github.event.pull_request.number }}'
          disable-reviews: true
