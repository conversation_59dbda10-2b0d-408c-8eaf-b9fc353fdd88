name: docker build

on:
  workflow_call:
    inputs:
      tag:
        description: The tag for the image to be used (e.g. `v2.9.0`). If not specified, `sha-<short SHA>` will be used.
        required: false
        type: string
      tag-latest:
        description: Whether to tag the image as `latest`.
        type: boolean
        default: false
      platforms:
        description: The platforms to build for.
        type: string
        default: linux/amd64
    outputs:
      image:
        description: The image name and tag.
        value: ${{ jobs.build.outputs.image }}

permissions:
  contents: read

jobs:
  prepare-tags:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    runs-on: ubuntu-latest
    outputs:
      tags: ${{ steps.merge-tags.outputs.tags }}
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Parse semver string
        if: ${{ inputs.tag != '' }}
        id: parse-semver-tag
        uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
        with:
          input_string: ${{ inputs.tag }}
          version_extractor_regex: 'v(.*)$'

      - uses: benjlevesque/short-sha@599815c8ee942a9616c92bcfb4f947a3b670ab0b # v3.0
        id: short-sha

      - name: Add standard tag
        id: add-standard-tag
        run: |
          if [ -z "${{ inputs.tag }}" ]; then
            echo "tag=type=raw,value=sha-${{ steps.short-sha.outputs.sha }}" >> $GITHUB_OUTPUT
          else
            echo "tag=type=raw,value=${{ steps.parse-semver-tag.outputs.fullversion }}" >> $GITHUB_OUTPUT
          fi

      - name: Add major.minor tag
        id: add-major-minor-tag
        if: ${{ steps.parse-semver-tag.outputs.prerelease == '' && inputs.tag != '' }}
        run: |
          echo "tag=type=raw,value=${{ steps.parse-semver-tag.outputs.major }}.${{ steps.parse-semver-tag.outputs.minor }}" >> $GITHUB_OUTPUT

      - name: Merge tags
        id: merge-tags
        run: |
          echo "tags<<EOF" >> $GITHUB_OUTPUT
          if [ -n "${{ steps.add-major-minor-tag.outputs.tag }}" ]; then
            echo "${{ steps.add-major-minor-tag.outputs.tag }}" >> $GITHUB_OUTPUT
          fi
          echo "${{ steps.add-standard-tag.outputs.tag }}" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Print tags
        run: |
          echo "tags: ${{ steps.merge-tags.outputs.tags }}"

  build:
    timeout-minutes: ${{ fromJSON(vars.GHA_EXTENDED_TIMEOUT_MINUTES || 60) }}
    runs-on: ubuntu-latest
    needs: prepare-tags
    outputs:
      image: kong/kubernetes-ingress-controller:${{ steps.meta.outputs.version }}
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      # Setup Golang to use go pkg cache which is utilized in Dockerfile's cache mount.
      - name: Setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod
      - run: echo "GOPATH=$(go env GOPATH)" >> $GITHUB_ENV
      - run: echo "GOCACHE=$(go env GOCACHE)" >> $GITHUB_ENV

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0

      - name: Cache Docker layers
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        with:
          images: kong/kubernetes-ingress-controller
          flavor: |
            latest=${{ inputs.tag-latest }}
          tags: ${{ needs.prepare-tags.outputs.tags }}

      - name: Build
        id: docker-build-dockerhub
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          push: false
          file: Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
          target: distroless
          platforms: ${{ inputs.platforms }}
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git
            GOPATH=${{ env.GOPATH}}
            GOCACHE=${{ env.GOCACHE}}

      # Build locally with outputs set to `type=docker,dest=/tmp/image.tar` to save the image as a `kic-image` artifact.
      - name: Build locally
        id: docker-build-local
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          load: true
          file: Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
          target: distroless
          outputs: type=docker,dest=/tmp/image.tar
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git
            GOPATH=${{ env.GOPATH}}
            GOCACHE=${{ env.GOCACHE}}

      - name: Upload image artifact
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: kic-image
          path: /tmp/image.tar
