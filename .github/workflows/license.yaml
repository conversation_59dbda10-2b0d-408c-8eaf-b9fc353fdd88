name: FOSSA

on:
  schedule:
    - cron: '30 2 * * *'
  workflow_dispatch: {}

permissions:
  contents: read

jobs:
  fossa-scan:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: fossas/fossa-action@3ebcea1862c6ffbd5cf1b4d0bd6b3fe7bd6f2cac # v1.7.0
        with:
          api-key: ${{ secrets.FOSSA_API_KEY }}
          branch: main
