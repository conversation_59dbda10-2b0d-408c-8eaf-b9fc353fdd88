name: release

on:
  workflow_dispatch:
    inputs:
      tag:
        description: |
          The version to release.
          Depending on the value, a production release will be published to GitHub or not.
            - In case of prerelease tags (e.g. v1.2.3-alpha.1) it will build-push the images (only standard tags,
              i.e., v1.2.3-alpha.1), test them and publish a GitHub prerelease (labeled as non-production ready).
            - In other cases (e.g. v1.2.3) it will build-push the images (standard and supplemental tags,
              i.e., v1.2.3 and v1.2), test them and publish a production Github release.
        required: true
      latest:
        description: 'Whether to tag this release latest'
        required: true
        type: boolean
        default: false

permissions:
  contents: read

jobs:
  verify-manifest-tag:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    outputs:
      fullversion_tag: ${{ steps.semver_parser.outputs.fullversion }}
    steps:
      - uses: mukunku/tag-exists-action@bdad1eaa119ce71b150b952c97351c75025c06a9 # v1.6.0
        id: check-tag
        name: check if tag already exists
        with:
          tag: ${{ github.event.inputs.tag }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: fail if tag already exists
        if: ${{ steps.check-tag.outputs.exists == 'true' }}
        run: exit 1
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Parse semver string
        id: semver_parser
        uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
        with:
          input_string: ${{ github.event.inputs.tag }}
          version_extractor_regex: 'v(.*)$'
      - name: Verify manifests have requested KIC tag
        if: ${{ steps.semver_parser.outputs.prerelease == '' }}
        env:
          # We expect the tag used in manifests to be {major}.{minor} part of the version, e.g.
          # for v2.10.3 we expect manifests to use 2.10 tag.
          TAG: ${{ steps.semver_parser.outputs.major }}.${{ steps.semver_parser.outputs.minor }}
        run: make verify.versions

  build-push-images:
    timeout-minutes: ${{ fromJSON(vars.GHA_EXTENDED_TIMEOUT_MINUTES) }}
    environment: 'Docker Push'
    needs: verify-manifest-tag
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Parse semver string
        id: semver_parser
        uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
        with:
          input_string: ${{ github.event.inputs.tag }}
          version_extractor_regex: 'v(.*)$'
      - name: Add standard tags
        run: |
          (
            echo 'TAGS_STANDARD<<EOF'
            echo 'type=raw,value=${{ steps.semver_parser.outputs.fullversion }}'
            echo 'EOF'
          ) >> $GITHUB_ENV
      - name: Add major.minor tag
        if: ${{ steps.semver_parser.outputs.prerelease == '' }}
        run: |
          (
            echo 'TAGS_SUPPLEMENTAL<<EOF'
            echo ""
            echo 'type=raw,value=${{ steps.semver_parser.outputs.major }}.${{ steps.semver_parser.outputs.minor }}'
            echo 'EOF'
          ) >> $GITHUB_ENV
      # Setup Golang to use go pkg cache which is utilized in Dockerfile's cache mount.
      - name: Setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod
      - run: echo "GOPATH=$(go env GOPATH)" >> $GITHUB_ENV
      - run: echo "GOCACHE=$(go env GOCACHE)" >> $GITHUB_ENV
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0
      - name: Cache Docker layers
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        with:
          images: kong/kubernetes-ingress-controller
          flavor: |
            latest=${{ github.event.inputs.latest == 'true' }}
          tags: ${{ env.TAGS_STANDARD }}${{ env.TAGS_SUPPLEMENTAL }}
      - name: Build binary
        id: docker_build_binary
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          push: false
          file: Dockerfile
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
          target: builder
          platforms: linux/amd64, linux/arm64
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git
            GOPATH=${{ env.GOPATH}}
            GOCACHE=${{ env.GOCACHE}}
      - name: Login to DockerHub
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PUSH_TOKEN }}
      - name: Build and push distroless image to DockerHub
        id: docker_build
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          push: true
          file: Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=local,src=/tmp/.buildx-cache
          target: distroless
          platforms: linux/amd64, linux/arm64
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git
            GOPATH=${{ env.GOPATH}}
            GOCACHE=${{ env.GOCACHE}}

  test-e2e:
    needs: [verify-manifest-tag, build-push-images]
    uses: ./.github/workflows/_e2e_tests.yaml
    secrets: inherit
    with:
      kic-image: kong/kubernetes-ingress-controller:${{ needs.verify-manifest-tag.outputs.fullversion_tag }}
      all-supported-k8s-versions: true

  publish-release:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    needs: [build-push-images, test-e2e]
    permissions:
      contents: write
    steps:
    - name: Parse semver string
      id: semver_parser
      uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
      with:
        input_string: ${{ github.event.inputs.tag }}
        version_extractor_regex: 'v(.*)$'
    - uses: ncipollo/release-action@440c8c1cb0ed28b9f43e4d1d670870f059653174 # v1.16.0
      with:
        body: |
          #### Download Kong Ingress Controller ${{ steps.semver_parser.outputs.fullversion }}:

          - [Docker Image](https://hub.docker.com/repository/docker/kong/kubernetes-ingress-controller)
          - [Get started](https://github.com/Kong/kubernetes-ingress-controller#get-started)

          #### Links:

          - [Changelog](https://github.com/Kong/kubernetes-ingress-controller/blob/main/CHANGELOG.md#${{ steps.semver_parser.outputs.major }}${{ steps.semver_parser.outputs.minor }}${{ steps.semver_parser.outputs.patch }})

        token: ${{ secrets.GITHUB_TOKEN }}
        tag: ${{ github.event.inputs.tag }}
        commit: ${{ github.sha }}
        # When prerelease part of the input tag is not empty, make it a prerelease.
        # The release will be labeled as non-production ready in GitHub.
        prerelease: ${{ steps.semver_parser.outputs.prerelease != '' }}
        makeLatest: ${{ github.event.inputs.latest == 'true' }}

  update-latest-branch:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    if: github.event.inputs.latest == 'true'
    permissions:
      contents: write
    needs:
    - publish-release
    steps:
    - name: checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        fetch-depth: 0
    - name: Parse semver string
      id: semver_parser
      uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
      with:
        input_string: ${{ github.event.inputs.tag }}
        version_extractor_regex: 'v(.*)$'
    - name: update 'latest' branch
      run: |
        git checkout latest
        git reset --hard v${{ steps.semver_parser.outputs.major }}.${{ steps.semver_parser.outputs.minor }}.${{ steps.semver_parser.outputs.patch }}
        git push -f origin latest
