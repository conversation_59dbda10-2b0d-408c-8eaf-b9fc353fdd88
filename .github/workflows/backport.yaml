name: Backport
on:
  pull_request:
    types:
      - closed
      - labeled

permissions:
  contents: read

jobs:
  backport:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    name: Backport
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    # Only react to merged PRs for security reasons.
    # See https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#pull_request_target.
    if: >
      github.event.pull_request.merged
      && (
        github.event.action == 'closed'
        || (
          github.event.action == 'labeled'
          && contains(github.event.label.name, 'backport')
        )
      )
    steps:
      - uses: tibdex/backport@9565281eda0731b1d20c4025c43339fb0a23812e # v2.0.4
        with:
          github_token: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}
