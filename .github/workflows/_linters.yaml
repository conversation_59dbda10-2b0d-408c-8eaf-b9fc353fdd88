name: linters

on:
  workflow_call: {}

permissions:
  contents: read

jobs:
  lint:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - name: Run lint
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
          # Our .golangci.yaml has fix: true, but we don't want that in CI therefore the below override.
          GOLANGCI_LINT_FLAGS: "--fix=false"
        run: make lint

      - name: Verify manifest consistency
        run: make verify.manifests

      - name: Verify generators consistency
        run: make verify.generators

  actionlint:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - name: Run actionlint
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
        run: make lint.actions
