name: performance tests (nightly)

on:
  schedule:
    - cron: '30 5 * * *'
  workflow_dispatch: {}

permissions:
  contents: read

jobs:
  ensure-nightly-image-was-built:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    steps:
      - name: Check if image built this night exists
        run: docker pull kong/nightly-ingress-controller:$(date +%Y-%m-%d)

  performance-tests:
    needs: ensure-nightly-image-was-built
    uses: ./.github/workflows/_performance_tests.yaml
    secrets: inherit
    with:
      kic-image: kong/nightly-ingress-controller:nightly

  test-reports:
    needs:
      - performance-tests
    uses: ./.github/workflows/_test_reports.yaml
    secrets: inherit
    with:
      coverage: false

  performance-reports:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    needs: performance-tests
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: setup ruby
        uses: ruby/setup-ruby@a4effe49ee8ee5b8b5091268c473a4628afb5651 # v1.245.0
        with:
          ruby-version: 3.2
          bundler-cache: true
      - name: install uplot
        run: |
          gem install youplot

      - name: download performance test results
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          pattern: performance-tests-results-*
          path: perf-results
          merge-multiple: true

      - name: drawing
        run: |
          cat perf-results/all_resource_apply*.txt >> result_of_all_resource_apply
          uplot bar -d " " --title="Performance test for KIC" --ylabel="number of resources" --xlabel="time(ms) cost for all routes to apply to cluster" result_of_all_resource_apply

          cat perf-results/all_resource_take_effect*.txt >> result_of_all_resource_take_effect
          uplot bar -d " " --title="Performance test for KIC" --ylabel="number of resources" --xlabel="time(ms) cost for all routes to take effect." result_of_all_resource_take_effect

          cat perf-results/one_resource_update*.txt >> result_of_one_resource_update
          uplot bar -d " " --title="Performance test for KIC" --ylabel="number of resources" --xlabel="time(ms) cost for update one ingress." result_of_one_resource_update

          cat perf-results/one_resource_take_effect*.txt >> result_of_one_resource_take_effect
          uplot bar -d " " --title="Performance test for KIC" --ylabel="number of resources" --xlabel="time(ms) cost for update one ingress to take effect." result_of_one_resource_take_effect

  notify-on-slack:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    runs-on: ubuntu-latest
    needs:
      - ensure-nightly-image-was-built
      - performance-tests
      - test-reports
    if: always() && contains(needs.*.result, 'failure') && github.event_name == 'schedule'
    steps:
      - name: Notify on Slack for failures of performance tests run automatically at night
        uses: 8398a7/action-slack@1750b5085f3ec60384090fb7c52965ef822e869e # v3
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        with:
          # Since notify-on-slack is triggered on failure (if statement), we can hardcode this.
          status: failure
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: ':red_circle: performance tests failed for nightly run, please check why.'
