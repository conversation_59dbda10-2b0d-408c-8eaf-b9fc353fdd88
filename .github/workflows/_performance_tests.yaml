name: performance tests

on:
  workflow_call:
    inputs:
      kic-image:
        description: KIC image to be tested (e.g. `kong/kubernetes-ingress-controller:v2.9.0`).
        type: string
        required: true
      kong-image:
        description: Kong image to be tested (e.g. `kong:3.1.0`).
        type: string
        required: false
      kong-effective-version:
        description: Effective version of Kong (e.g. `3.4`). Used when testing against nightly Kong images.
        type: string
        required: false
      load-local-image:
        description: Whether to load the local built image into the Docker daemon (from artifact).
        type: boolean
        default: false
      res-number-for-perf:
        description: The number of resource for performance tests
        type: string

permissions:
  contents: read

jobs:
  performance-matrix:
    timeout-minutes: ${{ fromJSON(vars.GHA_EXTENDED_TIMEOUT_MINUTES || 60) }}
    if: ${{ inputs.res-number-for-perf == '' }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        resource-number: [1000, 2500, 5000]
      fail-fast: false
    steps:
      - name: Download built image artifact
        if: ${{ inputs.load-local-image }}
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: kic-image
          path: /tmp

      - name: Load built image
        if: ${{ inputs.load-local-image }}
        run: |
          docker load --input /tmp/image.tar
          docker image ls -a

      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: Kong/kong-license@c4decf08584f84ff8fe8e7cd3c463e0192f6111b # master @ ********
        id: license
        with:
          op-token: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}

      - name: split image and tag
        id: split
        env:
          KONG: ${{ inputs.kong-image }}
        run: |
          if [ "${{ inputs.kong-image }}" != "" ]; then
            export kong_image=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[1]}')
            export kong_tag=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kong-image=$kong_image" >> $GITHUB_OUTPUT
            echo "kong-tag=$kong_tag" >> $GITHUB_OUTPUT
          fi
          if [ "${{ inputs.kic-image }}" != "" ]; then
            export kic_image=$(echo ${{ inputs.kic-image }} | awk '{split($0,a,":"); print a[1]}')
            export kic_tag=$(echo ${{ inputs.kic-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kic-image=$kic_image" >> $GITHUB_OUTPUT
            echo "kic-tag=$kic_tag" >> $GITHUB_OUTPUT
          fi

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - name: run performance tests
        run: make test.performance
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
          TEST_CONTROLLER_IMAGE: ${{ steps.split.outputs.kic-image }}
          TEST_CONTROLLER_TAG: ${{ steps.split.outputs.kic-tag }}
          TEST_KONG_LOAD_IMAGES: ${{ inputs.load-local-image }}
          TEST_KONG_IMAGE: ${{ steps.split.outputs.kong-image }}
          TEST_KONG_TAG: ${{ steps.split.outputs.kong-tag }}
          KONG_LICENSE_DATA: ${{ steps.license.outputs.license }}
          PERF_RES_NUMBER: ${{ matrix.resource-number }}
          NCPU: 1 # it was found that github actions (specifically) did not seem to perform well when spawning
          # multiple kind clusters within a single job, so only 1 is allowed at a time.
          GOTESTSUM_JUNITFILE: "performance-tests.xml"
          E2E_TEST_TIMEOUT: 180m

      - name: upload diagnostics
        if: ${{ always() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: diagnostics-performance-tests-${{ matrix.resource-number }}
          path: /tmp/ktf-diag*
          if-no-files-found: ignore

      - name: collect test report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: tests-report-performance-${{ matrix.resource-number }}
          path: "*-tests.xml"

      - name: collect performance test results
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: performance-tests-results-${{ matrix.resource-number }}
          path: "/tmp/kic-perf/*.txt"
          if-no-files-found: ignore

  performance-target:
    timeout-minutes: ${{ fromJSON(vars.GHA_EXTENDED_TIMEOUT_MINUTES || 60) }}
    if: ${{ inputs.res-number-for-perf != '' }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - name: Download built image artifact
        if: ${{ inputs.load-local-image }}
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: kic-image
          path: /tmp

      - name: Load built image
        if: ${{ inputs.load-local-image }}
        run: |
          docker load --input /tmp/image.tar
          docker image ls -a

      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: Kong/kong-license@c4decf08584f84ff8fe8e7cd3c463e0192f6111b # master @ ********
        id: license
        with:
          op-token: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}

      - name: split image and tag
        id: split
        env:
          KONG: ${{ inputs.kong-image }}
        run: |
          if [ "${{ inputs.kong-image }}" != "" ]; then
            export kong_image=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[1]}')
            export kong_tag=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kong-image=$kong_image" >> $GITHUB_OUTPUT
            echo "kong-tag=$kong_tag" >> $GITHUB_OUTPUT
          fi
          if [ "${{ inputs.kic-image }}" != "" ]; then
            export kic_image=$(echo ${{ inputs.kic-image }} | awk '{split($0,a,":"); print a[1]}')
            export kic_tag=$(echo ${{ inputs.kic-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kic-image=$kic_image" >> $GITHUB_OUTPUT
            echo "kic-tag=$kic_tag" >> $GITHUB_OUTPUT
          fi

      - name: run performance tests
        run: make test.performance
        env:
          TEST_CONTROLLER_IMAGE: ${{ steps.split.outputs.kic-image }}
          TEST_CONTROLLER_TAG: ${{ steps.split.outputs.kic-tag }}
          TEST_KONG_LOAD_IMAGES: ${{ inputs.load-local-image }}
          TEST_KONG_IMAGE: ${{ steps.split.outputs.kong-image }}
          TEST_KONG_TAG: ${{ steps.split.outputs.kong-tag }}
          KONG_LICENSE_DATA: ${{ steps.license.outputs.license }}
          PERF_RES_NUMBER: ${{ inputs.res-number-for-perf }}
          NCPU: 1 # it was found that github actions (specifically) did not seem to perform well when spawning
          # multiple kind clusters within a single job, so only 1 is allowed at a time.
          GOTESTSUM_JUNITFILE: "performance-tests.xml"
          E2E_TEST_TIMEOUT: 180m

      - name: upload diagnostics
        if: ${{ always() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: diagnostics-performance-target-tests-${{ inputs.res-number-for-perf }}
          path: /tmp/ktf-diag*
          if-no-files-found: ignore

      - name: collect test report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: tests-report-performance-target-tests-${{ inputs.res-number-for-perf }}
          path: "*-tests.xml"

      - name: collect performance test results
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: performance-target-tests-results-${{ inputs.res-number-for-perf }}
          path: "/tmp/kic-perf/*.txt"
          if-no-files-found: ignore
