name: "validate Kong image (targeted)"
run-name: "validate Kong ${{ format('{0}:{1}', github.event.inputs.kong-image-repo, github.event.inputs.kong-image-tag) }}"

on:
  workflow_dispatch:
    inputs:
      kong-image-repo:
        description: Kong Gateway Docker image to test with (repository). Must be an EE variant.
        type: string
        required: true
        default: "kong/kong-gateway"
      kong-image-tag:
        description: Kong Gateway Docker image to test with (tag).
        type: string
        required: true
        default: "latest"
      kong-effective-version:
        description: Effective semantic version of Kong Gateway Docker image. If not given, the semantic version will be parsed from the image tag.
        type: string
        required: false
      e2e-controller-image-repo:
        description: KIC Docker image for E2E tests (repository).
        type: string
        required: true
        default: "kong/kubernetes-ingress-controller"
      e2e-controller-image-tag:
        description: KIC Docker image for E2E tests (tag).
        type: string
        required: true
        default: "latest"
      issue-number:
        description: Issue number to comment in, and close in case of success. Can be none.
        type: string
        required: false

permissions:
  contents: read

jobs:
  startup-issue-comment:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    if: ${{ github.event.inputs.issue-number != '' }}
    runs-on: ubuntu-latest
    env:
      GH_TOKEN: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}
      # URL is the current workflow's run URL.
      # Sadly this is not readily available in github's context.
      URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
      ISSUE_NUMBER: ${{ github.event.inputs.issue-number }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - run: |
          gh issue comment ${ISSUE_NUMBER} --body \
            'Kong Gateway validation tests were started at ${{ env.URL }} with the following parameters:
            ```json
            ${{ toJSON(github.event.inputs) }}
            ```
            '

  run-e2e-tests:
    if: ${{ !cancelled() }}
    uses: ./.github/workflows/_e2e_tests.yaml
    secrets: inherit
    with:
      kic-image: ${{ format('{0}:{1}', github.event.inputs.e2e-controller-image-repo, github.event.inputs.e2e-controller-image-tag) }}
      kong-image: ${{ format('{0}:{1}', github.event.inputs.kong-image-repo, github.event.inputs.kong-image-tag) }}
      load-local-image: false
      run-gke: true
      run-istio: true
      all-supported-k8s-versions: true

  run-integration-tests:
    if: ${{ !cancelled() }}
    uses: ./.github/workflows/_integration_tests.yaml
    secrets: inherit
    with:
      kong-container-repo: ${{ github.event.inputs.kong-image-repo }}
      kong-container-tag: ${{ github.event.inputs.kong-image-tag }}
      kong-oss-effective-version: ${{ github.event.inputs.kong-effective-version }}
      # We're passing the same image twice, because the integration tests need to know the image
      # for Enterprise variant of tests separately.
      # That makes this workflow usable only for Enterprise images.
      kong-enterprise-container-repo: ${{ github.event.inputs.kong-image-repo }}
      kong-enterprise-container-tag: ${{ github.event.inputs.kong-image-tag }}
      kong-enterprise-effective-version: ${{ github.event.inputs.kong-effective-version }}
      log-output-file:  /tmp/integration-tests-kic-logs

  on-finish-comment-or-close-issue:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
    needs:
    - startup-issue-comment
    - run-e2e-tests
    - run-integration-tests
    if: ${{ always() && github.event.inputs.issue-number != '' }}
    runs-on: ubuntu-latest
    permissions:
      contents: write
      issues: write
    env:
      GH_TOKEN: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}
      URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
      ISSUE_NUMBER: ${{ github.event.inputs.issue-number }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - if: ${{ !contains(needs.*.result, 'failure') && !contains(needs.*.result, 'cancelled') }}
        run: |
          gh issue close ${ISSUE_NUMBER} --comment \
            'Kong Gateway validation tests **PASSED** ✅ at ${{ env.URL }} with the following parameters:
            ```json
            ${{ toJSON(github.event.inputs) }}
            ```
            '
      - if: ${{ contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled') }}
        run: |
          gh issue comment ${ISSUE_NUMBER} --body \
            'Kong Gateway validation tests **FAILED** ❌ at ${{ env.URL }} with the following parameters:
            ```json
            ${{ toJSON(github.event.inputs) }}
            ```
            '
