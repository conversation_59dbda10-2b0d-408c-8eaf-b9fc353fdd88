name: e2e tests

on:
  workflow_call:
    inputs:
      kic-image:
        description: KIC image to be tested (e.g. `kong/kubernetes-ingress-controller:v2.9.0`).
        type: string
        required: true
      kong-image:
        description: Kong image to be tested (e.g. `kong:3.1.0`).
        type: string
        required: false
      kong-effective-version:
        description: Effective version of Kong (e.g. `3.4`). Used when testing against nightly Kong images.
        type: string
        required: false
      load-local-image:
        description: Whether to load the local built image into the Docker daemon (from artifact).
        type: boolean
        default: false
      all-supported-k8s-versions:
        description: Run tests against all supported Kubernetes versions. Otherwise, only against the latest one.
        type: boolean
        default: false
      run-gke:
        description: Run E2E tests on GKE as well as on Kind.
        type: boolean
        default: false
      run-istio:
        description: Run Istio E2E tests.
        type: boolean
        default: false

permissions:
  contents: read

jobs:
  setup-e2e-tests:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    runs-on: ubuntu-latest
    outputs:
      test_names: ${{ steps.set_test_names.outputs.test_names }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - id: setup_golang
        name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - id: test_files
        name: Get test file names
        working-directory: test/e2e/
        # go list used to extract the test names from only those test files that
        # match the specified tags: here e2e_tests.
        # This filters out e.g. istio tests which we run separately.
        run: echo "result=$(go list -tags e2e_tests -f '{{ range .TestGoFiles }} {{- . }} {{ end }}' .)" >> $GITHUB_OUTPUT

      - name: Print test file names
        run: echo "Test file names ${{ steps.test_files.outputs.result }}"

      - id: set_test_names
        name: Set test names
        working-directory: test/e2e/
        # grep magic described in https://unix.stackexchange.com/a/13472
        # sed to add the extra $ is because some of our test names overlap. we need it so the -run regex only matches one test
        run: |
          echo "test_names=$(grep -shoP "(?<=^func )(Test[a-zA-z_0-9]+)(?=\(t \*testing.T\) {)" ${{ steps.test_files.outputs.result }} | sed -e "s/$/\$/"| jq -R . | jq -cs .)" >> $GITHUB_OUTPUT

      - name: Print test names
        run: echo "Test names ${{ steps.set_test_names.outputs.test_names }}"

  dependencies-versions:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    runs-on: ubuntu-latest
    outputs:
      kind: ${{ steps.set-versions.outputs.kind }}
      gke: ${{ steps.set-versions.outputs.gke }}
      istio: ${{ steps.set-versions.outputs.istio }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - id: set-versions
        name: Set versions
        run: |
          if [ "${{ inputs.all-supported-k8s-versions }}" == "true" ]; then
            echo "kind=$(yq -r -ojson '.e2e.kind' < .github/test_dependencies.yaml | jq -c)" >> $GITHUB_OUTPUT
          else
            # We assume the first version in the list is the latest one.
            echo "kind=$(yq -r -ojson '.e2e.kind' < .github/test_dependencies.yaml | jq -c '[first]')" >> $GITHUB_OUTPUT
          fi
          echo "gke=$(yq -r -ojson '.e2e.gke' < .github/test_dependencies.yaml | jq -c)" >> $GITHUB_OUTPUT
          echo "istio=$(yq -r -ojson '.e2e.istio' < .github/test_dependencies.yaml | jq -c)" >> $GITHUB_OUTPUT

  kind:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    runs-on: ubuntu-latest
    if: ${{ !cancelled() }}
    needs:
      - setup-e2e-tests
      - dependencies-versions
    strategy:
      fail-fast: false
      matrix:
        kubernetes-version: ${{ fromJSON(needs.dependencies-versions.outputs.kind) }}
        test: ${{ fromJSON(needs.setup-e2e-tests.outputs.test_names) }}
    steps:
      - name: Download built image artifact
        if: ${{ inputs.load-local-image }}
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: kic-image
          path: /tmp

      - name: Load built image
        if: ${{ inputs.load-local-image }}
        run: |
          docker load --input /tmp/image.tar
          docker image ls -a

      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: Kong/kong-license@c4decf08584f84ff8fe8e7cd3c463e0192f6111b # master @ ********
        id: license
        with:
          op-token: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}

      - name: split image and tag
        id: split
        env:
          KONG: ${{ inputs.kong-image }}
        run: |
          if [ "${{ inputs.kong-image }}" != "" ]; then
            export kong_image=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[1]}')
            export kong_tag=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kong-image=$kong_image" >> $GITHUB_OUTPUT
            echo "kong-tag=$kong_tag" >> $GITHUB_OUTPUT
          fi
          if [ "${{ inputs.kic-image }}" != "" ]; then
            export kic_image=$(echo ${{ inputs.kic-image }} | awk '{split($0,a,":"); print a[1]}')
            export kic_tag=$(echo ${{ inputs.kic-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kic-image=$kic_image" >> $GITHUB_OUTPUT
            echo "kic-tag=$kic_tag" >> $GITHUB_OUTPUT
          fi

      # We need to pull the Gateway image locally if loading local image was specified.
      # This is a "workaround" of the fact that we bind the env variable - responsible for
      # indicating whether we'd like to load the images - for both controller
      # and gateway. Hence when that's set to true we try to load the Gateway
      # image which is available in the external container image registry but not
      # locally and that fails.
      - name: Pull Gateway image for tests
        if: ${{ inputs.kong-image != '' && inputs.load-local-image }}
        run: docker pull ${{ inputs.kong-image }}

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - run: echo "GOTESTSUM_JUNITFILE=e2e-kind-${{ matrix.test }}-${{ matrix.kubernetes-version }}-tests.xml" >> $GITHUB_ENV

      - name: run ${{ matrix.test }}
        run: make test.e2e
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
          E2E_TEST_RUN: ${{ matrix.test }}
          KONG_CLUSTER_VERSION: ${{ matrix.kubernetes-version }}
          TEST_CONTROLLER_IMAGE: ${{ steps.split.outputs.kic-image }}
          TEST_CONTROLLER_TAG: ${{ steps.split.outputs.kic-tag }}
          TEST_KONG_LOAD_IMAGES: ${{ inputs.load-local-image }}
          TEST_KONG_IMAGE: ${{ steps.split.outputs.kong-image }}
          TEST_KONG_TAG: ${{ steps.split.outputs.kong-tag }}
          TEST_KONG_EFFECTIVE_VERSION: ${{ inputs.kong-effective-version }}
          TEST_KONG_KONNECT_ACCESS_TOKEN: ${{ secrets.K8S_TEAM_KONNECT_ACCESS_TOKEN }}
          KONG_LICENSE_DATA: ${{ steps.license.outputs.license }}
          GOTESTSUM_JUNITFILE: ${{ env.GOTESTSUM_JUNITFILE }}

      - name: Set KONG_IMAGE_ARTIFACT
        if: ${{ always() }}
        run: |
          if [ "${{ steps.split.outputs.kong-image }}" != "" ]; then
            export kong_image=$( echo ${{ steps.split.outputs.kong-image }} | awk '{gsub(/[:.\/]/, "-")};$1' )-${{ steps.split.outputs.kong-tag }}
          fi
          if [ "${{ steps.split.outputs.kic-image }}" != "" ]; then
            export kic_image=$( echo ${{ steps.split.outputs.kic-image }} | awk '{gsub(/[:.\/]/, "-")};$1' )-${{ steps.split.outputs.kic-tag }}
          fi
          echo "KONG_IMAGE_ARTIFACT=kong_image-${kong_image}-kic_image-${kic_image}" >> $GITHUB_ENV

      - name: upload diagnostics
        if: ${{ always() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: diagnostics-e2e-tests-${{ matrix.test }}-${{ matrix.kubernetes-version }}-${{ env.KONG_IMAGE_ARTIFACT }}
          path: /tmp/ktf-diag*
          if-no-files-found: ignore

      - name: collect test report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: tests-report-e2e-kind-${{ matrix.test }}-${{ matrix.kubernetes-version }}-${{ env.KONG_IMAGE_ARTIFACT }}
          path: ${{ env.GOTESTSUM_JUNITFILE }}

  gke:
    timeout-minutes: ${{ fromJSON(vars.GHA_EXTENDED_TIMEOUT_MINUTES || 60) }}
    if: ${{ inputs.run-gke }}
    environment: "gcloud"
    runs-on: ubuntu-latest
    needs:
      - setup-e2e-tests
      - dependencies-versions
    strategy:
      fail-fast: false
      matrix:
        kubernetes-version: ${{ fromJSON(needs.dependencies-versions.outputs.gke) }}
        test: ${{ fromJSON(needs.setup-e2e-tests.outputs.test_names) }}
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: Kong/kong-license@c4decf08584f84ff8fe8e7cd3c463e0192f6111b # master @ ********
        continue-on-error: true
        id: license
        with:
          op-token: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}

      - name: check availability of KIC image
        id: check_kic_image
        if: ${{ inputs.kic-image != '' }}
        # We have to check whether the image passed in inputs is available in the docker hub.
        # If it's not, we'll fall back to a nightly image later.
        run: (docker manifest inspect ${{ inputs.kic-image }} && echo "kic_image=${{ inputs.kic-image }}" >> $GITHUB_OUTPUT) || true

      - name: split image and tag
        id: split
        env:
          KONG: ${{ inputs.kong-image }}
        run: |
          if [ "${{ inputs.kong-image }}" != "" ]; then
            export kong_image=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[1]}')
            export kong_tag=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kong-image=$kong_image" >> $GITHUB_OUTPUT
            echo "kong-tag=$kong_tag" >> $GITHUB_OUTPUT
          fi
          if [ "${{ steps.check_kic_image.outputs.kic_image }}" != "" ]; then
            export kic_image_repo=$(echo ${{ steps.check_kic_image.outputs.kic_image }} | awk '{split($0,a,":"); print a[1]}')
            export kic_image_tag=$(echo ${{ steps.check_kic_image.outputs.kic_image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kic-image=$kic_image_repo" >> $GITHUB_OUTPUT
            echo "kic-tag=$kic_image_tag" >> $GITHUB_OUTPUT
          else
            # See the https://github.com/Kong/kubernetes-testing-framework/issues/587 TODO below.
            # If we add local image GKE support, we can get rid of this fallback.
            echo "kic-image=kong/nightly-ingress-controller" >> $GITHUB_OUTPUT
            echo "kic-tag=nightly" >> $GITHUB_OUTPUT
          fi

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - run: echo "GOTESTSUM_JUNITFILE=e2e-gke-${{ matrix.test }}-${{ matrix.kubernetes-version }}-tests.xml" >> $GITHUB_ENV

      - name: run ${{ matrix.test }}
        run: make test.e2e
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
          # NOTE: The limitation of the GKE setup is that we cannot run tests against a local image,
          # therefore we need to use the nightly one.
          # TODO: Once we have a way to load images into GKE, we can use the local image.
          # KTF issue that should enable it: https://github.com/Kong/kubernetes-testing-framework/issues/587
          TEST_CONTROLLER_IMAGE: ${{ steps.split.outputs.kic-image }}
          TEST_CONTROLLER_TAG: ${{ steps.split.outputs.kic-tag }}
          TEST_KONG_IMAGE: ${{ steps.split.outputs.kong-image }}
          TEST_KONG_TAG: ${{ steps.split.outputs.kong-tag }}
          TEST_KONG_EFFECTIVE_VERSION: ${{ inputs.kong-effective-version }}
          KONG_LICENSE_DATA: ${{ steps.license.outputs.license }}
          TEST_KONG_KONNECT_ACCESS_TOKEN: ${{ secrets.K8S_TEAM_KONNECT_ACCESS_TOKEN }}
          KONG_CLUSTER_VERSION: ${{ matrix.kubernetes-version }}
          KONG_CLUSTER_PROVIDER: gke
          E2E_TEST_RUN: ${{ matrix.test }}
          GOTESTSUM_JUNITFILE: ${{ env.GOTESTSUM_JUNITFILE }}
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
          GOOGLE_PROJECT: ${{ secrets.GOOGLE_PROJECT }}
          GOOGLE_LOCATION: ${{ secrets.GOOGLE_LOCATION }}
          TEST_GKE_CLUSTER_RELEASE_CHANNEL: "rapid"

      - name: Set KONG_IMAGE_ARTIFACT
        if: ${{ always() }}
        run: |
          if [ "${{ steps.split.outputs.kong-image }}" != "" ]; then
            export kong_image=$( echo ${{ steps.split.outputs.kong-image }} | awk '{gsub(/[:.\/]/, "-")};$1' )-${{ steps.split.outputs.kong-tag }}
          fi
          if [ "${{ steps.split.outputs.kic-image }}" != "" ]; then
            export kic_image=$( echo ${{ steps.split.outputs.kic-image }} | awk '{gsub(/[:.\/]/, "-")};$1' )-${{ steps.split.outputs.kic-tag }}
          fi
          echo "KONG_IMAGE_ARTIFACT=kong_image-${kong_image}-kic_image-${kic_image}" >> $GITHUB_ENV

      - name: upload diagnostics
        if: ${{ always() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          # NOTE: we add the kong image suffix here because actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
          # only allows to have 1 artifact with the same name and we run this workflow
          # in parallel for different kong images (specifically for nightly).
          name: diagnostics-e2e-gke-tests-${{ matrix.test }}-${{ matrix.kubernetes-version }}-${{ env.KONG_IMAGE_ARTIFACT }}
          path: /tmp/ktf-diag*
          if-no-files-found: ignore

      - name: collect test report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          # NOTE: we add the kong image suffix here because actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
          # only allows to have 1 artifact with the same name and we run this workflow
          # in parallel for different kong images (specifically for nightly).
          name: tests-report-e2e-gke-${{ matrix.test }}-${{ matrix.kubernetes-version }}-${{ env.KONG_IMAGE_ARTIFACT }}
          path: ${{ env.GOTESTSUM_JUNITFILE }}

  istio:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    if: ${{ inputs.run-istio }}
    runs-on: ubuntu-latest
    needs: dependencies-versions
    strategy:
      fail-fast: false
      matrix:
        include: ${{ fromJSON(needs.dependencies-versions.outputs.istio) }}
    steps:
      - name: Download built image artifact
        if: ${{ inputs.load-local-image }}
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: kic-image
          path: /tmp

      - name: Load built image
        if: ${{ inputs.load-local-image }}
        run: |
          docker load --input /tmp/image.tar
          docker image ls -a

      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: Kong/kong-license@c4decf08584f84ff8fe8e7cd3c463e0192f6111b # master @ ********
        id: license
        with:
          op-token: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}

      - name: split image and tag
        id: split
        env:
          KONG: ${{ inputs.kong-image }}
        run: |
          if [ "${{ inputs.kong-image }}" != "" ]; then
            export kong_image=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[1]}')
            export kong_tag=$(echo ${{ inputs.kong-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kong-image=$kong_image" >> $GITHUB_OUTPUT
            echo "kong-tag=$kong_tag" >> $GITHUB_OUTPUT
          fi
          if [ "${{ inputs.kic-image }}" != "" ]; then
            export kic_image=$(echo ${{ inputs.kic-image }} | awk '{split($0,a,":"); print a[1]}')
            export kic_tag=$(echo ${{ inputs.kic-image }} | awk '{split($0,a,":"); print a[2]}')
            echo "kic-image=$kic_image" >> $GITHUB_OUTPUT
            echo "kic-tag=$kic_tag" >> $GITHUB_OUTPUT
          fi

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - run: echo "GOTESTSUM_JUNITFILE=e2e-istio-${{ matrix.kind }}-${{ matrix.istio }}-tests.xml" >> $GITHUB_ENV

      - name: run Istio tests
        run: make test.istio
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
          TEST_CONTROLLER_IMAGE: ${{ steps.split.outputs.kic-image }}
          TEST_CONTROLLER_TAG: ${{ steps.split.outputs.kic-tag }}
          TEST_KONG_LOAD_IMAGES: ${{ inputs.load-local-image }}
          TEST_KONG_IMAGE: ${{ steps.split.outputs.kong-image }}
          TEST_KONG_TAG: ${{ steps.split.outputs.kong-tag }}
          KONG_LICENSE_DATA: ${{ steps.license.outputs.license }}
          KONG_CLUSTER_VERSION: ${{ matrix.kind }}
          ISTIO_VERSION: ${{ matrix.istio }}
          NCPU: 1 # it was found that github actions (specifically) did not seem to perform well when spawning
          # multiple kind clusters within a single job, so only 1 is allowed at a time.
          GOTESTSUM_JUNITFILE: ${{ env.GOTESTSUM_JUNITFILE }}

      - name: Set KONG_IMAGE_ARTIFACT
        if: ${{ always() }}
        run: |
          if [ "${{ steps.split.outputs.kong-image }}" != "" ]; then
            export kong_image=$( echo ${{ steps.split.outputs.kong-image }} | awk '{gsub(/[:.\/]/, "-")};$1' )-${{ steps.split.outputs.kong-tag }}
          fi
          if [ "${{ steps.split.outputs.kic-image }}" != "" ]; then
            export kic_image=$( echo ${{ steps.split.outputs.kic-image }} | awk '{gsub(/[:.\/]/, "-")};$1' )-${{ steps.split.outputs.kic-tag }}
          fi
          echo "KONG_IMAGE_ARTIFACT=kong_image-${kong_image}-kic_image-${kic_image}" >> $GITHUB_ENV

      - name: upload diagnostics
        if: ${{ always() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          # NOTE: we add the kong image suffix here because actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
          # only allows to have 1 artifact with the same name and we run this workflow
          # in parallel for different kong images (specifically for nightly).
          name: diagnostics-e2e-tests-istio-${{ matrix.kind }}-${{ matrix.istio }}-${{ env.KONG_IMAGE_ARTIFACT }}
          path: /tmp/ktf-diag*
          if-no-files-found: ignore

      - name: collect test report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          # NOTE: we add the kong image suffix here because actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
          # only allows to have 1 artifact with the same name and we run this workflow
          # in parallel for different kong images (specifically for nightly).
          name: tests-report-e2e-istio-k8s-${{ matrix.kind }}-istio-${{ matrix.istio }}-${{ env.KONG_IMAGE_ARTIFACT }}
          path: ${{ env.GOTESTSUM_JUNITFILE }}
