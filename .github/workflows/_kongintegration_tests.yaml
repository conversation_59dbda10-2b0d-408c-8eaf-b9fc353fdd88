name: kong integration tests

on:
  workflow_call: {}

permissions:
  contents: read

jobs:
  kongintegration-tests:
    timeout-minutes: ${{ fromJSON(vars.GHA_EXTENDED_TIMEOUT_MINUTES || 60) }}
    runs-on: ubuntu-latest
    name: ${{ matrix.name }}
    strategy:
      matrix:
        include:
          - name: enterprise
            enterprise: true
          - name: oss
            enterprise: false
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: Kong/kong-license@c4decf08584f84ff8fe8e7cd3c463e0192f6111b # master @ ********
        id: license
        with:
          op-token: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}

      - name: set kong oss version
        if: ${{ !matrix.enterprise }}
        run: |
          echo "TEST_KONG_IMAGE=kong" >> $GITHUB_ENV
          echo "TEST_KONG_TAG=$(yq -ojson -r '.kongintegration.kong-oss' < .github/test_dependencies.yaml )" >> $GITHUB_ENV

      - name: set kong ee version
        if: ${{ matrix.enterprise }}
        run: |
          echo "TEST_KONG_IMAGE=kong/kong-gateway" >> $GITHUB_ENV
          echo "TEST_KONG_TAG=$(yq -ojson -r '.kongintegration.kong-ee' < .github/test_dependencies.yaml )" >> $GITHUB_ENV

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - run: echo "GOTESTSUM_JUNITFILE=kongintegration-${{ matrix.name }}-tests.xml" >> $GITHUB_ENV

      - name: run kong integration tests
        run: make test.kongintegration
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
          GOTESTSUM_JUNITFILE: ${{ env.GOTESTSUM_JUNITFILE }}
          TEST_KONG_KONNECT_ACCESS_TOKEN: ${{ secrets.K8S_TEAM_KONNECT_ACCESS_TOKEN }}
          KONG_LICENSE_DATA: ${{ steps.license.outputs.license }}
          TEST_KONG_ENTERPRISE: ${{ matrix.enterprise }}

      - name: collect test coverage
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: coverage-kongintegration-${{ matrix.name }}
          path: coverage.*.out

      - name: collect test report
        if: always()
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: tests-report-kongintegration-${{ matrix.name }}
          path: ${{ env.GOTESTSUM_JUNITFILE }}
