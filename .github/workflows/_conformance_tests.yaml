name: conformance tests

on:
  workflow_call:
    inputs:
      log-output-file:
        # specifies the file for KIC manager's logs to output to.
        type: string
        default: ""
        required: false

permissions:
  contents: read

jobs:
  dependencies-versions:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    runs-on: ubuntu-latest
    outputs:
      helm-kong: ${{ steps.set-versions.outputs.helm-kong }}
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - id: set-versions
        name: Set versions
        run: |
          echo "helm-kong=$(yq -ojson -r '.integration.helm.kong' < .github/test_dependencies.yaml )" >> $GITHUB_OUTPUT

  conformance-tests:
    timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT || 10) }}
    name: ${{ matrix.name }}
    runs-on: ubuntu-latest
    needs: dependencies-versions
    env:
      TEST_KONG_HELM_CHART_VERSION: ${{ needs.dependencies-versions.outputs.helm-kong }}
    strategy:
      fail-fast: false
      matrix:
        include:
        - name: traditional-compatible-router
          router-flavor: traditional_compatible
        - name: expressions-router
          router-flavor: expressions
    steps:
      - name: checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: setup golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod

      - uses: jdx/mise-action@13abe502c30c1559a5c37dff303831bab82c9402 # v2.2.3
        with:
          install: false

      - run: echo "JUNIT_REPORT=conformance-tests-${{ matrix.name }}.xml" >> $GITHUB_ENV

      - name: run conformance tests
        run: make test.conformance
        env:
          MISE_VERBOSE: 1
          MISE_DEBUG: 1
          JUNIT_REPORT: ${{ env.JUNIT_REPORT }}
          TEST_KONG_ROUTER_FLAVOR: ${{ matrix.router-flavor }}
          TEST_KONG_KIC_MANAGER_LOG_OUTPUT: ${{ inputs.log-output-file }}

      # upload logs when test failed
      - name: upload KIC logs
        if: ${{ failure() && inputs.log-output-file != '' }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with: 
          name: ${{ matrix.name }}-kic-logs
          path: ${{ inputs.log-output-file }}
          if-no-files-found: ignore

      - name: collect test report
        if: ${{ always() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: test-report-conformance-${{ matrix.name }}
          path: ${{ env.JUNIT_REPORT }}

  merge-junit-test-reports:
    runs-on: ubuntu-latest
    needs:
    - conformance-tests
    steps:
      - name: Merge Junit test reports
        uses: actions/upload-artifact/merge@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: test-report-conformance
          pattern: test-report-conformance-*
          delete-merged: true
