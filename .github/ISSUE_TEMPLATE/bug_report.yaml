name: 🐞 Bug
description: Something is not working as indended.
labels: [bug]
body:
- type: checkboxes
  attributes:
    label: Is there an existing issue for this?
    description: Please search to see if an issue already exists for the bug you encountered.
    options:
    - label: I have searched the existing issues
      required: true
- type: textarea
  attributes:
    label: Current Behavior
    description: A concise description of what you're experiencing.
    placeholder: |
      When I do <X>, <Y> happens and I see the error message attached below:
      ```...```
  validations:
    required: false
- type: textarea
  attributes:
    label: Expected Behavior
    description: A concise description of what you expected to happen.
    placeholder: When I do <X>, <Z> should happen instead.
  validations:
    required: false
- type: textarea
  attributes:
    label: Steps To Reproduce
    description: Steps to reproduce the behavior.
    placeholder: |
      1. In this environment...
      2. With this config...
      3. Run '...'
      4. See error...
    render: markdown
  validations:
    required: false
- type: textarea
  attributes:
    label: Kong Ingress Controller version
    description: |
      example: v1.3.1
    placeholder: 'Please provide your version of KIC here.'
    render: shell
  validations:
    required: false
- type: textarea
  attributes:
    label: Kubernetes version
    description: |
      example:
        Client Version: version.Info{Major:"1", Minor:"20", GitVersion:"v1.20.2", GitCommit:"faecb196815e248d3ecfb03c680a4507229c2a56", GitTreeState:"clean", BuildDate:"2021-01-13T13:28:09Z", GoVersion:"go1.15.5", Compiler:"gc", Platform:"linux/amd64"}
        Server Version: version.Info{Major:"1", Minor:"20", GitVersion:"v1.20.2", GitCommit:"faecb196815e248d3ecfb03c680a4507229c2a56", GitTreeState:"clean", BuildDate:"2021-01-21T01:11:42Z", GoVersion:"go1.15.5", Compiler:"gc", Platform:"linux/amd64"}
    placeholder: 'Please provide your version of Kubernetes here.'
    render: shell
  validations:
    required: false
- type: textarea
  attributes:
    label: Anything else?
    description: |
      Links? References? Anything that will give us more context about the issue you are encountering?

      Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
  validations:
    required: false
