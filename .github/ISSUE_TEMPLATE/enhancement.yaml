name: ✨ Enhancement / Feature / Task
description: Some feature is missing or incomplete.
labels: [enhancement]
body:
- type: checkboxes
  attributes:
    label: Is there an existing issue for this?
    description: Please search to see if an issue already exists for the bug you encountered.
    options:
    - label: I have searched the existing issues
      required: true
- type: checkboxes
  attributes:
    label: Does this enhancement require public documentation?
    description: |
      Please make sure that Acceptance Criteria section includes for updating our public documentation
      ([docs.konghq.com](https://github.com/kong/docs.konghq.com), README.md, etc.) with the new feature or enhancement.
      If the scope of the change is too large to be documented in the Acceptance Criteria, please create a separate
      issue and link it in the AC.
    options:
      - label: I have added an Acceptance Criteria item for adding and/or adjusting public documentation (if applicable)
        required: true
- type: textarea
  attributes:
    label: Problem Statement
    description: Without specifying a solution, describe what the project is missing today.
    placeholder: |
      The rotating project logo has a fixed size and color.
      There is no way to make it larger and more shiny.
  validations:
    required: false
- type: textarea
  attributes:
    label: Proposed Solution
    description: Describe the proposed solution to the problem above.
    placeholder: |
      - Implement 2 new flags CLI: ```--logo-color=FFD700``` and ```--logo-size=100```
      - Let these flags control the size of the rotating project logo.
  validations:
    required: false
- type: textarea
  attributes:
    label: Additional information
    placeholder: |
      We considered adjusting the logo size to the phase of the moon, but there was no
      reliable data source in air-gapped environments.
  validations:
    required: false
- type: textarea
  attributes:
    label: Acceptance Criteria
    placeholder: |
      - [ ] As a user, I can control the size of the rotating logo using a CLI flag.
      - [ ] As a user, I can control the color of the rotating logo using a CLI flag.
      - [ ] Defaults are reasonably set.
      - [ ] New settings are appropriately documented.
      - [ ] No breaking change for current users of the rotating logo feature.
  validations:
    required: false
