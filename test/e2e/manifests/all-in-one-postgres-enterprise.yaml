# Generated by build-single-manifest.sh. NOT FOR PRODUCTION USE (only used internally for testing). DO NOT EDIT.

apiVersion: v1
kind: Namespace
metadata:
  name: kong
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: ingressclassparameterses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    kind: IngressClassParameters
    listKind: IngressClassParametersList
    plural: ingressclassparameterses
    singular: ingressclassparameters
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: IngressClassParameters is the Schema for the IngressClassParameters
          API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the IngressClassParameters specification.
            properties:
              enableLegacyRegexDetection:
                default: false
                description: |-
                  EnableLegacyRegexDetection automatically detects if ImplementationSpecific Ingress paths are regular expression
                  paths using the legacy 2.x heuristic. The controller adds the "~" prefix to those paths if the Kong version is
                  3.0 or higher.
                type: boolean
              serviceUpstream:
                default: false
                description: Offload load-balancing to kube-proxy or sidecar.
                type: boolean
            type: object
        type: object
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongclusterplugins.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongClusterPlugin
    listKind: KongClusterPluginList
    plural: kongclusterplugins
    shortNames:
    - kcp
    singular: kongclusterplugin
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Name of the plugin
      jsonPath: .plugin
      name: Plugin-Type
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Indicates if the plugin is disabled
      jsonPath: .disabled
      name: Disabled
      priority: 1
      type: boolean
    - description: Configuration of the plugin
      jsonPath: .config
      name: Config
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: KongClusterPlugin is the Schema for the kongclusterplugins API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          config:
            description: |-
              Config contains the plugin configuration. It's a list of keys and values
              required to configure the plugin.
              Please read the documentation of the plugin being configured to set values
              in here. For any plugin in Kong, anything that goes in the `config` JSON
              key in the Admin API request, goes into this property.
              Only one of `config` or `configFrom` may be used in a KongClusterPlugin, not both at once.
            type: object
            x-kubernetes-preserve-unknown-fields: true
          configFrom:
            description: |-
              ConfigFrom references a secret containing the plugin configuration.
              This should be used when the plugin configuration contains sensitive information,
              such as AWS credentials in the Lambda plugin or the client secret in the OIDC plugin.
              Only one of `config` or `configFrom` may be used in a KongClusterPlugin, not both at once.
            properties:
              secretKeyRef:
                description: Specifies a name, a namespace, and a key of a secret
                  to refer to.
                properties:
                  key:
                    description: The key containing the value.
                    type: string
                  name:
                    description: The secret containing the key.
                    type: string
                  namespace:
                    description: The namespace containing the secret.
                    type: string
                required:
                - key
                - name
                - namespace
                type: object
            required:
            - secretKeyRef
            type: object
          configPatches:
            description: |-
              ConfigPatches represents JSON patches to the configuration of the plugin.
              Each item means a JSON patch to add something in the configuration,
              where path is specified in `path` and value is in `valueFrom` referencing
              a key in a secret.
              When Config is specified, patches will be applied to the configuration in Config.
              Otherwise, patches will be applied to an empty object.
            items:
              description: |-
                NamespacedConfigPatch is a JSON patch to add values from secrets to KongClusterPlugin
                to the generated configuration of plugin in Kong.
              properties:
                path:
                  description: Path is the JSON path to add the patch.
                  type: string
                valueFrom:
                  description: ValueFrom is the reference to a key of a secret where
                    the patched value comes from.
                  properties:
                    secretKeyRef:
                      description: Specifies a name, a namespace, and a key of a secret
                        to refer to.
                      properties:
                        key:
                          description: The key containing the value.
                          type: string
                        name:
                          description: The secret containing the key.
                          type: string
                        namespace:
                          description: The namespace containing the secret.
                          type: string
                      required:
                      - key
                      - name
                      - namespace
                      type: object
                  required:
                  - secretKeyRef
                  type: object
              required:
              - path
              - valueFrom
              type: object
            type: array
          consumerRef:
            description: ConsumerRef is a reference to a particular consumer.
            type: string
          disabled:
            description: Disabled set if the plugin is disabled or not.
            type: boolean
          instance_name:
            description: |-
              InstanceName is an optional custom name to identify an instance of the plugin. This is useful when running the
              same plugin in multiple contexts, for example, on multiple services.
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          ordering:
            description: |-
              Ordering overrides the normal plugin execution order. It's only available on Kong Enterprise.
              `<phase>` is a request processing phase (for example, `access` or `body_filter`) and
              `<plugin>` is the name of the plugin that will run before or after the KongPlugin.
              For example, a KongPlugin with `plugin: rate-limiting` and `before.access: ["key-auth"]`
              will create a rate limiting plugin that limits requests _before_ they are authenticated.
            properties:
              after:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
              before:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
            type: object
          plugin:
            description: PluginName is the name of the plugin to which to apply the
              config.
            type: string
          protocols:
            description: |-
              Protocols configures plugin to run on requests received on specific
              protocols.
            items:
              description: |-
                KongProtocol is a valid Kong protocol.
                This alias is necessary to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342
              enum:
              - http
              - https
              - grpc
              - grpcs
              - tcp
              - tls
              - udp
              type: string
            type: array
          run_on:
            description: |-
              RunOn configures the plugin to run on the first or the second or both
              nodes in case of a service mesh deployment.
            enum:
            - first
            - second
            - all
            type: string
          status:
            description: Status represents the current status of the KongClusterPlugin
              resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KongClusterPluginStatus.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - plugin
        type: object
        x-kubernetes-validations:
        - message: Using both config and configFrom fields is not allowed.
          rule: '!(has(self.config) && has(self.configFrom))'
        - message: Using both configFrom and configPatches fields is not allowed.
          rule: '!(has(self.configFrom) && has(self.configPatches))'
        - message: The plugin field is immutable
          rule: self.plugin == oldSelf.plugin
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller,gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongconsumergroups.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongConsumerGroup
    listKind: KongConsumerGroupList
    plural: kongconsumergroups
    shortNames:
    - kcg
    singular: kongconsumergroup
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: KongConsumerGroup is the Schema for the kongconsumergroups API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongConsumerGroupSpec defines the desired state of KongConsumerGroup.
            properties:
              controlPlaneRef:
                description: ControlPlaneRef is a reference to a ControlPlane this
                  ConsumerGroup is associated with.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                    type: string
                  konnectNamespacedRef:
                    description: |-
                      KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                      It contains the name of the Konnect Control Plane.
                      This field is required when the Type is konnectNamespacedRef.
                    properties:
                      name:
                        description: Name is the name of the Konnect Control Plane.
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace where the Konnect Control Plane is in.
                          Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    default: kic
                    description: |-
                      Type indicates the type of the control plane being referenced. Allowed values:
                      - konnectID
                      - konnectNamespacedRef
                      - kic

                      The default is kic, which implies that the Control Plane is KIC.
                    enum:
                    - konnectID
                    - konnectNamespacedRef
                    - kic
                    type: string
                type: object
                x-kubernetes-validations:
                - message: '''konnectID'' type is not supported'
                  rule: self.type != 'konnectID'
                - message: when type is konnectNamespacedRef, konnectNamespacedRef
                    must be set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? has(self.konnectNamespacedRef) : true'
                - message: when type is konnectNamespacedRef, konnectID must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? has(self.konnectID)
                    : true'
                - message: when type is konnectID, konnectNamespacedRef must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is kic, konnectID must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                    : true'
                - message: when type is kic, konnectNamespacedRef must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is unset, konnectID must not be set
                  rule: '!has(self.type) ? !has(self.konnectID) : true'
                - message: when type is unset, konnectNamespacedRef must not be set
                  rule: '!has(self.type) ? !has(self.konnectNamespacedRef) : true'
              name:
                description: Name is the name of the ConsumerGroup in Kong.
                type: string
              tags:
                description: Tags is an optional set of tags applied to the ConsumerGroup.
                items:
                  type: string
                maxItems: 20
                type: array
                x-kubernetes-validations:
                - message: tags entries must not be longer than 128 characters
                  rule: self.all(tag, size(tag) >= 1 && size(tag) <= 128)
            type: object
          status:
            default:
              conditions:
              - lastTransitionTime: "1970-01-01T00:00:00Z"
                message: Waiting for controller
                reason: Pending
                status: Unknown
                type: Programmed
            description: Status represents the current status of the KongConsumerGroup
              resource.
            properties:
              conditions:
                description: |-
                  Conditions describe the current conditions of the KongConsumerGroup.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              konnect:
                description: Konnect contains the Konnect entity status.
                properties:
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this Route is associated with.
                    type: string
                  id:
                    description: |-
                      ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                      If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                    type: string
                  organizationID:
                    description: OrgID is ID of Konnect Org that this entity has been
                      created in.
                    type: string
                  serverURL:
                    description: ServerURL is the URL of the Konnect server in which
                      the entity exists.
                    type: string
                type: object
            type: object
        type: object
        x-kubernetes-validations:
        - message: controlPlaneRef is required once set
          rule: (!has(oldSelf.spec) || !has(oldSelf.spec.controlPlaneRef)) || has(self.spec.controlPlaneRef)
        - message: spec.controlPlaneRef cannot specify namespace for namespaced resource
          rule: '(!has(self.spec) || !has(self.spec.controlPlaneRef) || !has(self.spec.controlPlaneRef.konnectNamespacedRef))
            ? true : !has(self.spec.controlPlaneRef.konnectNamespacedRef.__namespace__)'
        - message: spec.controlPlaneRef is immutable when an entity is already Programmed
          rule: '(!has(oldSelf.spec) || !has(oldSelf.spec.controlPlaneRef)) ? true
            : (!has(self.status) || !self.status.conditions.exists(c, c.type == ''Programmed''
            && c.status == ''True'')) ? true : oldSelf.spec.controlPlaneRef == self.spec.controlPlaneRef'
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller,gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongconsumers.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongConsumer
    listKind: KongConsumerList
    plural: kongconsumers
    shortNames:
    - kc
    singular: kongconsumer
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Username of a Kong Consumer
      jsonPath: .username
      name: Username
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: KongConsumer is the Schema for the kongconsumers API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          consumerGroups:
            description: |-
              ConsumerGroups are references to consumer groups (that consumer wants to be part of)
              provisioned in Kong.
            items:
              type: string
            type: array
            x-kubernetes-list-type: set
          credentials:
            description: |-
              Credentials are references to secrets containing a credential to be
              provisioned in Kong.
            items:
              type: string
            type: array
            x-kubernetes-list-type: set
          custom_id:
            description: |-
              CustomID is a Kong cluster-unique existing ID for the consumer - useful for mapping
              Kong with users in your existing database.
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongConsumerSpec defines the specification of the KongConsumer.
            properties:
              controlPlaneRef:
                description: ControlPlaneRef is a reference to a ControlPlane this
                  Consumer is associated with.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                    type: string
                  konnectNamespacedRef:
                    description: |-
                      KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                      It contains the name of the Konnect Control Plane.
                      This field is required when the Type is konnectNamespacedRef.
                    properties:
                      name:
                        description: Name is the name of the Konnect Control Plane.
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace where the Konnect Control Plane is in.
                          Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    default: kic
                    description: |-
                      Type indicates the type of the control plane being referenced. Allowed values:
                      - konnectID
                      - konnectNamespacedRef
                      - kic

                      The default is kic, which implies that the Control Plane is KIC.
                    enum:
                    - konnectID
                    - konnectNamespacedRef
                    - kic
                    type: string
                type: object
                x-kubernetes-validations:
                - message: '''konnectID'' type is not supported'
                  rule: self.type != 'konnectID'
                - message: when type is konnectNamespacedRef, konnectNamespacedRef
                    must be set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? has(self.konnectNamespacedRef) : true'
                - message: when type is konnectNamespacedRef, konnectID must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? has(self.konnectID)
                    : true'
                - message: when type is konnectID, konnectNamespacedRef must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is kic, konnectID must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                    : true'
                - message: when type is kic, konnectNamespacedRef must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is unset, konnectID must not be set
                  rule: '!has(self.type) ? !has(self.konnectID) : true'
                - message: when type is unset, konnectNamespacedRef must not be set
                  rule: '!has(self.type) ? !has(self.konnectNamespacedRef) : true'
              tags:
                description: Tags is an optional set of tags applied to the consumer.
                items:
                  type: string
                maxItems: 20
                type: array
                x-kubernetes-validations:
                - message: tags entries must not be longer than 128 characters
                  rule: self.all(tag, size(tag) >= 1 && size(tag) <= 128)
            type: object
          status:
            default:
              conditions:
              - lastTransitionTime: "1970-01-01T00:00:00Z"
                message: Waiting for controller
                reason: Pending
                status: Unknown
                type: Programmed
            description: Status represents the current status of the KongConsumer
              resource.
            properties:
              conditions:
                description: |-
                  Conditions describe the current conditions of the KongConsumer.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              konnect:
                description: Konnect contains the Konnect entity status.
                properties:
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this Route is associated with.
                    type: string
                  id:
                    description: |-
                      ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                      If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                    type: string
                  organizationID:
                    description: OrgID is ID of Konnect Org that this entity has been
                      created in.
                    type: string
                  serverURL:
                    description: ServerURL is the URL of the Konnect server in which
                      the entity exists.
                    type: string
                type: object
            type: object
          username:
            description: Username is a Kong cluster-unique username of the consumer.
            type: string
        type: object
        x-kubernetes-validations:
        - message: Need to provide either username or custom_id
          rule: has(self.username) || has(self.custom_id)
        - message: controlPlaneRef is required once set
          rule: (!has(oldSelf.spec) || !has(oldSelf.spec.controlPlaneRef)) || has(self.spec.controlPlaneRef)
        - message: spec.controlPlaneRef cannot specify namespace for namespaced resource
          rule: '(!has(self.spec) || !has(self.spec.controlPlaneRef) || !has(self.spec.controlPlaneRef.konnectNamespacedRef))
            ? true : !has(self.spec.controlPlaneRef.konnectNamespacedRef.__namespace__)'
        - message: spec.controlPlaneRef is immutable when an entity is already Programmed
          rule: '(!has(self.spec) || !has(self.spec.controlPlaneRef)) ? true : (!has(self.status)
            || !self.status.conditions.exists(c, c.type == ''Programmed'' && c.status
            == ''True'')) ? true : oldSelf.spec.controlPlaneRef == self.spec.controlPlaneRef'
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongcustomentities.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongCustomEntity
    listKind: KongCustomEntityList
    plural: kongcustomentities
    shortNames:
    - kce
    singular: kongcustomentity
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: type of the Kong entity
      jsonPath: .spec.type
      name: Entity Type
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KongCustomEntity defines a "custom" Kong entity that KIC cannot
          support the entity type directly.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongCustomEntitySpec defines the specification of the KongCustomEntity.
            properties:
              controllerName:
                description: ControllerName specifies the controller that should reconcile
                  it, like ingress class.
                type: string
              fields:
                description: Fields defines the fields of the Kong entity itself.
                x-kubernetes-preserve-unknown-fields: true
              parentRef:
                description: |-
                  ParentRef references the kubernetes resource it attached to when its scope is "attached".
                  Currently only KongPlugin/KongClusterPlugin allowed. This will make the custom entity to be attached
                  to the entity(service/route/consumer) where the plugin is attached.
                properties:
                  group:
                    description: Group defines the API group of the referred object.
                    type: string
                  kind:
                    description: Kind defines the kind of the referred object.
                    enum:
                    - KongPlugin
                    - KongClusterPlugin
                    type: string
                  name:
                    description: Name defines the name of the referred object.
                    type: string
                  namespace:
                    description: Empty namespace means the same namespace of the owning
                      object.
                    type: string
                required:
                - name
                type: object
              type:
                description: EntityType is the type of the Kong entity. The type is
                  used in generating declarative configuration.
                type: string
            required:
            - controllerName
            - fields
            - type
            type: object
            x-kubernetes-validations:
            - message: The type field cannot be one of the known Kong entity types
              rule: '!(self.type in [''services'',''routes'',''upstreams'',''targets'',''plugins'',''consumers'',''consumer_groups''])'
          status:
            description: Status stores the reconciling status of the resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KongCustomEntityStatus.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            required:
            - conditions
            type: object
        required:
        - spec
        type: object
        x-kubernetes-validations:
        - message: The spec.type field is immutable
          rule: self.spec.type == oldSelf.spec.type
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongingresses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongIngress
    listKind: KongIngressList
    plural: kongingresses
    shortNames:
    - ki
    singular: kongingress
  scope: Namespaced
  versions:
  - deprecated: true
    deprecationWarning: configuration.konghq.com/v1 KongIngress is deprecated
    name: v1
    schema:
      openAPIV3Schema:
        description: |-
          KongIngress is the Schema for the kongingresses API.
          Deprecated: Use Gateway API instead.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          proxy:
            description: |-
              Proxy defines additional connection options for the routes to be configured in the
              Kong Gateway, e.g. `connection_timeout`, `retries`, etc.
            properties:
              connect_timeout:
                description: "The timeout in milliseconds for\testablishing a connection
                  to the upstream server.\nDeprecated: use Service's \"konghq.com/connect-timeout\"
                  annotation instead."
                minimum: 0
                type: integer
              path:
                description: |-
                  (optional) The path to be used in requests to the upstream server.
                  Deprecated: use Service's "konghq.com/path" annotation instead.
                pattern: ^/.*$
                type: string
              protocol:
                description: |-
                  The protocol used to communicate with the upstream.
                  Deprecated: use Service's "konghq.com/protocol" annotation instead.
                enum:
                - http
                - https
                - grpc
                - grpcs
                - tcp
                - tls
                - udp
                type: string
              read_timeout:
                description: |-
                  The timeout in milliseconds between two successive read operations
                  for transmitting a request to the upstream server.
                  Deprecated: use Service's "konghq.com/read-timeout" annotation instead.
                minimum: 0
                type: integer
              retries:
                description: |-
                  The number of retries to execute upon failure to proxy.
                  Deprecated: use Service's "konghq.com/retries" annotation instead.
                minimum: 0
                type: integer
              write_timeout:
                description: |-
                  The timeout in milliseconds between two successive write operations
                  for transmitting a request to the upstream server.
                  Deprecated: use Service's "konghq.com/write-timeout" annotation instead.
                minimum: 0
                type: integer
            type: object
          route:
            description: |-
              Route define rules to match client requests.
              Each Route is associated with a Service,
              and a Service may have multiple Routes associated to it.
            properties:
              headers:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: |-
                  Headers contains one or more lists of values indexed by header name
                  that will cause this Route to match if present in the request.
                  The Host header cannot be used with this attribute.
                  Deprecated: use Ingress' "konghq.com/headers" annotation instead.
                type: object
              https_redirect_status_code:
                description: |-
                  HTTPSRedirectStatusCode is the status code Kong responds with
                  when all properties of a Route match except the protocol.
                  Deprecated: use Ingress' "ingress.kubernetes.io/force-ssl-redirect" or
                  "konghq.com/https-redirect-status-code" annotations instead.
                type: integer
              methods:
                description: |-
                  Methods is a list of HTTP methods that match this Route.
                  Deprecated: use Ingress' "konghq.com/methods" annotation instead.
                items:
                  type: string
                type: array
              path_handling:
                description: |-
                  PathHandling controls how the Service path, Route path and requested path
                  are combined when sending a request to the upstream.
                  Deprecated: use Ingress' "konghq.com/path-handling" annotation instead.
                enum:
                - v0
                - v1
                type: string
              preserve_host:
                description: |-
                  PreserveHost sets When matching a Route via one of the hosts domain names,
                  use the request Host header in the upstream request headers.
                  If set to false, the upstream Host header will be that of the Service’s host.
                  Deprecated: use Ingress' "konghq.com/preserve-host" annotation instead.
                type: boolean
              protocols:
                description: |-
                  Protocols is an array of the protocols this Route should allow.
                  Deprecated: use Ingress' "konghq.com/protocols" annotation instead.
                items:
                  description: |-
                    KongProtocol is a valid Kong protocol.
                    This alias is necessary to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342
                  enum:
                  - http
                  - https
                  - grpc
                  - grpcs
                  - tcp
                  - tls
                  - udp
                  type: string
                type: array
              regex_priority:
                description: |-
                  RegexPriority is a number used to choose which route resolves a given request
                  when several routes match it using regexes simultaneously.
                  Deprecated: use Ingress' "konghq.com/regex-priority" annotation instead.
                type: integer
              request_buffering:
                description: |-
                  RequestBuffering sets whether to enable request body buffering or not.
                  Deprecated: use Ingress' "konghq.com/request-buffering" annotation instead.
                type: boolean
              response_buffering:
                description: |-
                  ResponseBuffering sets whether to enable response body buffering or not.
                  Deprecated: use Ingress' "konghq.com/response-buffering" annotation instead.
                type: boolean
              snis:
                description: |-
                  SNIs is a list of SNIs that match this Route when using stream routing.
                  Deprecated: use Ingress' "konghq.com/snis" annotation instead.
                items:
                  type: string
                type: array
              strip_path:
                description: |-
                  StripPath sets When matching a Route via one of the paths
                  strip the matching prefix from the upstream request URL.
                  Deprecated: use Ingress' "konghq.com/strip-path" annotation instead.
                type: boolean
            type: object
          upstream:
            description: |-
              Upstream represents a virtual hostname and can be used to loadbalance
              incoming requests over multiple targets (e.g. Kubernetes `Services` can
              be a target, OR `Endpoints` can be targets).
            properties:
              algorithm:
                description: |-
                  Algorithm is the load balancing algorithm to use.
                  Accepted values are: "round-robin", "consistent-hashing", "least-connections", "latency".
                enum:
                - round-robin
                - consistent-hashing
                - least-connections
                - latency
                type: string
              hash_fallback:
                description: |-
                  HashFallback defines What to use as hashing input
                  if the primary hash_on does not return a hash.
                  Accepted values are: "none", "consumer", "ip", "header", "cookie".
                type: string
              hash_fallback_header:
                description: |-
                  HashFallbackHeader is the header name to take the value from as hash input.
                  Only required when "hash_fallback" is set to "header".
                type: string
              hash_fallback_query_arg:
                description: HashFallbackQueryArg is the "hash_fallback" version of
                  HashOnQueryArg.
                type: string
              hash_fallback_uri_capture:
                description: HashFallbackURICapture is the "hash_fallback" version
                  of HashOnURICapture.
                type: string
              hash_on:
                description: |-
                  HashOn defines what to use as hashing input.
                  Accepted values are: "none", "consumer", "ip", "header", "cookie", "path", "query_arg", "uri_capture".
                type: string
              hash_on_cookie:
                description: |-
                  The cookie name to take the value from as hash input.
                  Only required when "hash_on" or "hash_fallback" is set to "cookie".
                type: string
              hash_on_cookie_path:
                description: |-
                  The cookie path to set in the response headers.
                  Only required when "hash_on" or "hash_fallback" is set to "cookie".
                type: string
              hash_on_header:
                description: |-
                  HashOnHeader defines the header name to take the value from as hash input.
                  Only required when "hash_on" is set to "header".
                type: string
              hash_on_query_arg:
                description: HashOnQueryArg is the query string parameter whose value
                  is the hash input when "hash_on" is set to "query_arg".
                type: string
              hash_on_uri_capture:
                description: |-
                  HashOnURICapture is the name of the capture group whose value is the hash input when "hash_on" is set to
                  "uri_capture".
                type: string
              healthchecks:
                description: Healthchecks defines the health check configurations
                  in Kong.
                properties:
                  active:
                    description: ActiveHealthcheck configures active health check
                      probing.
                    properties:
                      concurrency:
                        minimum: 1
                        type: integer
                      headers:
                        additionalProperties:
                          items:
                            type: string
                          type: array
                        type: object
                      healthy:
                        description: |-
                          Healthy configures thresholds and HTTP status codes
                          to mark targets healthy for an upstream.
                        properties:
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          successes:
                            minimum: 0
                            type: integer
                        type: object
                      http_path:
                        pattern: ^/.*$
                        type: string
                      https_sni:
                        type: string
                      https_verify_certificate:
                        type: boolean
                      timeout:
                        minimum: 0
                        type: integer
                      type:
                        type: string
                      unhealthy:
                        description: |-
                          Unhealthy configures thresholds and HTTP status codes
                          to mark targets unhealthy.
                        properties:
                          http_failures:
                            minimum: 0
                            type: integer
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          tcp_failures:
                            minimum: 0
                            type: integer
                          timeouts:
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  passive:
                    description: |-
                      PassiveHealthcheck configures passive checks around
                      passive health checks.
                    properties:
                      healthy:
                        description: |-
                          Healthy configures thresholds and HTTP status codes
                          to mark targets healthy for an upstream.
                        properties:
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          successes:
                            minimum: 0
                            type: integer
                        type: object
                      type:
                        type: string
                      unhealthy:
                        description: |-
                          Unhealthy configures thresholds and HTTP status codes
                          to mark targets unhealthy.
                        properties:
                          http_failures:
                            minimum: 0
                            type: integer
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          tcp_failures:
                            minimum: 0
                            type: integer
                          timeouts:
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  threshold:
                    type: number
                type: object
              host_header:
                description: |-
                  HostHeader is The hostname to be used as Host header
                  when proxying requests through Kong.
                type: string
              slots:
                description: Slots is the number of slots in the load balancer algorithm.
                minimum: 10
                type: integer
            type: object
        type: object
        x-kubernetes-validations:
        - message: '''proxy'' field is no longer supported, use Service''s annotations
            instead'
          rule: '!has(self.proxy)'
        - message: '''route'' field is no longer supported, use Ingress'' annotations
            instead'
          rule: '!has(self.route)'
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller,gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: konglicenses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongLicense
    listKind: KongLicenseList
    plural: konglicenses
    shortNames:
    - kl
    singular: konglicense
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Enabled to configure on Kong gateway instances
      jsonPath: .enabled
      name: Enabled
      type: boolean
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KongLicense stores a Kong enterprise license to apply to managed
          Kong gateway instances.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          enabled:
            default: true
            description: |-
              Enabled is set to true to let controllers (like KIC or KGO) to reconcile it.
              Default value is true to apply the license by default.
            type: boolean
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          rawLicenseString:
            description: RawLicenseString is a string with the raw content of the
              license.
            type: string
          status:
            description: Status is the status of the KongLicense being processed by
              controllers.
            properties:
              controllers:
                items:
                  description: |-
                    KongLicenseControllerStatus is the status of owning KongLicense being processed
                    identified by the controllerName field.
                  properties:
                    conditions:
                      default:
                      - lastTransitionTime: "1970-01-01T00:00:00Z"
                        message: Waiting for controller
                        reason: Pending
                        status: Unknown
                        type: Programmed
                      description: Conditions describe the current conditions of the
                        KongLicense on the controller.
                      items:
                        description: Condition contains details for one aspect of
                          the current state of this API Resource.
                        properties:
                          lastTransitionTime:
                            description: |-
                              lastTransitionTime is the last time the condition transitioned from one status to another.
                              This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                            format: date-time
                            type: string
                          message:
                            description: |-
                              message is a human readable message indicating details about the transition.
                              This may be an empty string.
                            maxLength: 32768
                            type: string
                          observedGeneration:
                            description: |-
                              observedGeneration represents the .metadata.generation that the condition was set based upon.
                              For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                              with respect to the current state of the instance.
                            format: int64
                            minimum: 0
                            type: integer
                          reason:
                            description: |-
                              reason contains a programmatic identifier indicating the reason for the condition's last transition.
                              Producers of specific condition types may define expected values and meanings for this field,
                              and whether the values are considered a guaranteed API.
                              The value should be a CamelCase string.
                              This field may not be empty.
                            maxLength: 1024
                            minLength: 1
                            pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                            type: string
                          status:
                            description: status of the condition, one of True, False,
                              Unknown.
                            enum:
                            - "True"
                            - "False"
                            - Unknown
                            type: string
                          type:
                            description: type of condition in CamelCase or in foo.example.com/CamelCase.
                            maxLength: 316
                            pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                            type: string
                        required:
                        - lastTransitionTime
                        - message
                        - reason
                        - status
                        - type
                        type: object
                      maxItems: 8
                      type: array
                      x-kubernetes-list-map-keys:
                      - type
                      x-kubernetes-list-type: map
                    controllerName:
                      description: |-
                        ControllerName is an identifier of the controller to reconcile this KongLicense.
                        Should be unique in the list of controller statuses.
                      type: string
                    controllerRef:
                      description: |-
                        ControllerRef is the reference of the controller to reconcile this KongLicense.
                        It is usually the name of (KIC/KGO) pod that reconciles it.
                      properties:
                        group:
                          description: |-
                            Group is the group of referent.
                            It should be empty if the referent is in "core" group (like pod).
                          maxLength: 253
                          pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        kind:
                          description: |-
                            Kind is the kind of the referent.
                            By default the nil kind means kind Pod.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                          type: string
                        name:
                          description: Name is the name of the referent.
                          maxLength: 253
                          minLength: 1
                          type: string
                        namespace:
                          description: |-
                            Namespace is the namespace of the referent.
                            It should be empty if the referent is cluster scoped.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                          type: string
                      required:
                      - name
                      type: object
                  required:
                  - controllerName
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - controllerName
                x-kubernetes-list-type: map
            type: object
        required:
        - enabled
        - rawLicenseString
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller,gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongplugins.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongPlugin
    listKind: KongPluginList
    plural: kongplugins
    shortNames:
    - kp
    singular: kongplugin
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Name of the plugin
      jsonPath: .plugin
      name: Plugin-Type
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Indicates if the plugin is disabled
      jsonPath: .disabled
      name: Disabled
      priority: 1
      type: boolean
    - description: Configuration of the plugin
      jsonPath: .config
      name: Config
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: KongPlugin is the Schema for the kongplugins API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          config:
            description: |-
              Config contains the plugin configuration. It's a list of keys and values
              required to configure the plugin.
              Please read the documentation of the plugin being configured to set values
              in here. For any plugin in Kong, anything that goes in the `config` JSON
              key in the Admin API request, goes into this property.
              Only one of `config` or `configFrom` may be used in a KongPlugin, not both at once.
            type: object
            x-kubernetes-preserve-unknown-fields: true
          configFrom:
            description: |-
              ConfigFrom references a secret containing the plugin configuration.
              This should be used when the plugin configuration contains sensitive information,
              such as AWS credentials in the Lambda plugin or the client secret in the OIDC plugin.
              Only one of `config` or `configFrom` may be used in a KongPlugin, not both at once.
            properties:
              secretKeyRef:
                description: Specifies a name and a key of a secret to refer to. The
                  namespace is implicitly set to the one of referring object.
                properties:
                  key:
                    description: The key containing the value.
                    type: string
                  name:
                    description: The secret containing the key.
                    type: string
                required:
                - key
                - name
                type: object
            required:
            - secretKeyRef
            type: object
          configPatches:
            description: |-
              ConfigPatches represents JSON patches to the configuration of the plugin.
              Each item means a JSON patch to add something in the configuration,
              where path is specified in `path` and value is in `valueFrom` referencing
              a key in a secret.
              When Config is specified, patches will be applied to the configuration in Config.
              Otherwise, patches will be applied to an empty object.
            items:
              description: |-
                ConfigPatch is a JSON patch (RFC6902) to add values from Secret to the generated configuration.
                It is an equivalent of the following patch:
                `{"op": "add", "path": {.Path}, "value": {.ComputedValueFrom}}`.
              properties:
                path:
                  description: Path is the JSON-Pointer value (RFC6901) that references
                    a location within the target configuration.
                  type: string
                valueFrom:
                  description: ValueFrom is the reference to a key of a secret where
                    the patched value comes from.
                  properties:
                    secretKeyRef:
                      description: Specifies a name and a key of a secret to refer
                        to. The namespace is implicitly set to the one of referring
                        object.
                      properties:
                        key:
                          description: The key containing the value.
                          type: string
                        name:
                          description: The secret containing the key.
                          type: string
                      required:
                      - key
                      - name
                      type: object
                  required:
                  - secretKeyRef
                  type: object
              required:
              - path
              - valueFrom
              type: object
            type: array
          consumerRef:
            description: ConsumerRef is a reference to a particular consumer.
            type: string
          disabled:
            description: Disabled set if the plugin is disabled or not.
            type: boolean
          instance_name:
            description: |-
              InstanceName is an optional custom name to identify an instance of the plugin. This is useful when running the
              same plugin in multiple contexts, for example, on multiple services.
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          ordering:
            description: |-
              Ordering overrides the normal plugin execution order. It's only available on Kong Enterprise.
              `<phase>` is a request processing phase (for example, `access` or `body_filter`) and
              `<plugin>` is the name of the plugin that will run before or after the KongPlugin.
              For example, a KongPlugin with `plugin: rate-limiting` and `before.access: ["key-auth"]`
              will create a rate limiting plugin that limits requests _before_ they are authenticated.
            properties:
              after:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
              before:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
            type: object
          plugin:
            description: PluginName is the name of the plugin to which to apply the
              config.
            type: string
          protocols:
            description: |-
              Protocols configures plugin to run on requests received on specific
              protocols.
            items:
              description: |-
                KongProtocol is a valid Kong protocol.
                This alias is necessary to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342
              enum:
              - http
              - https
              - grpc
              - grpcs
              - tcp
              - tls
              - udp
              type: string
            type: array
          run_on:
            description: |-
              RunOn configures the plugin to run on the first or the second or both
              nodes in case of a service mesh deployment.
            enum:
            - first
            - second
            - all
            type: string
          status:
            description: Status represents the current status of the KongPlugin resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KongPluginStatus.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - plugin
        type: object
        x-kubernetes-validations:
        - message: Using both config and configFrom fields is not allowed.
          rule: '!(has(self.config) && has(self.configFrom))'
        - message: Using both configFrom and configPatches fields is not allowed.
          rule: '!(has(self.configFrom) && has(self.configPatches))'
        - message: The plugin field is immutable
          rule: self.plugin == oldSelf.plugin
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller-incubator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongservicefacades.incubator.ingress-controller.konghq.com
spec:
  group: incubator.ingress-controller.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongServiceFacade
    listKind: KongServiceFacadeList
    plural: kongservicefacades
    singular: kongservicefacade
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          KongServiceFacade allows creating separate Kong Services for a single Kubernetes
          Service. It can be used as Kubernetes Ingress' backend (via its path's `backend.resource`
          field). It's designed to enable creating two "virtual" Services in Kong that will point
          to the same Kubernetes Service, but will have different configuration (e.g. different
          set of plugins, different load balancing algorithm, etc.).

          KongServiceFacade requires `kubernetes.io/ingress.class` annotation with a value
          matching the ingressClass of the Kong Ingress Controller (`kong` by default) to be reconciled.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongServiceFacadeSpec defines the desired state of KongServiceFacade.
            properties:
              backendRef:
                description: |-
                  Backend is a reference to a Kubernetes Service that is used as a backend
                  for this Kong Service Facade.
                properties:
                  name:
                    description: Name is the name of the referenced Kubernetes Service.
                    type: string
                  port:
                    description: Port is the port of the referenced Kubernetes Service.
                    format: int32
                    type: integer
                required:
                - name
                - port
                type: object
            required:
            - backendRef
            type: object
          status:
            description: KongServiceFacadeStatus defines the observed state of KongServiceFacade.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KongServiceFacade.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  labels:
    gateway.networking.k8s.io/policy: direct
  name: kongupstreampolicies.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongUpstreamPolicy
    listKind: KongUpstreamPolicyList
    plural: kongupstreampolicies
    shortNames:
    - kup
    singular: kongupstreampolicy
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: |-
          KongUpstreamPolicy allows configuring algorithm that should be used for load balancing traffic between Kong
          Upstream's Targets. It also allows configuring health checks for Kong Upstream's Targets.

          Its configuration is similar to Kong Upstream object (https://docs.konghq.com/gateway/latest/admin-api/#upstream-object),
          and it is applied to Kong Upstream objects created by the controller.

          It can be attached to Services. To attach it to a Service, it has to be annotated with
          `konghq.com/upstream-policy: <name>`, where `<name>` is the name of the KongUpstreamPolicy
          object in the same namespace as the Service.

          When attached to a Service, it will affect all Kong Upstreams created for the Service.

          When attached to a Service used in a Gateway API *Route rule with multiple BackendRefs, all of its Services MUST
          be configured with the same KongUpstreamPolicy. Otherwise, the controller will *ignore* the KongUpstreamPolicy.

          Note: KongUpstreamPolicy doesn't implement Gateway API's GEP-713 strictly.
          In particular, it doesn't use the TargetRef for attaching to Services and Gateway API *Routes - annotations are
          used instead. This is to allow reusing the same KongUpstreamPolicy for multiple Services and Gateway API *Routes.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec contains the configuration of the Kong upstream.
            properties:
              algorithm:
                description: |-
                  Algorithm is the load balancing algorithm to use.
                  Accepted values are: "round-robin", "consistent-hashing", "least-connections", "latency", "sticky-sessions.
                enum:
                - round-robin
                - consistent-hashing
                - least-connections
                - latency
                - sticky-sessions
                type: string
              hashOn:
                description: |-
                  HashOn defines how to calculate hash for consistent-hashing or sticky-sessions load balancing algorithm.
                  Algorithm must be set to "consistent-hashing" or "sticky-sessions" for this field to have effect.
                properties:
                  cookie:
                    description: Cookie is the name of the cookie to use as hash input.
                    type: string
                  cookiePath:
                    description: CookiePath is cookie path to set in the response
                      headers.
                    type: string
                  header:
                    description: Header is the name of the header to use as hash input.
                    type: string
                  input:
                    description: |-
                      Input allows using one of the predefined inputs (ip, consumer, path, none).
                      Set this to `none` if you want to use sticky sessions.
                      For other parametrized inputs, use one of the fields below.
                    enum:
                    - ip
                    - consumer
                    - path
                    - none
                    type: string
                  queryArg:
                    description: QueryArg is the name of the query argument to use
                      as hash input.
                    type: string
                  uriCapture:
                    description: URICapture is the name of the URI capture group to
                      use as hash input.
                    type: string
                type: object
              hashOnFallback:
                description: |-
                  HashOnFallback defines how to calculate hash for consistent-hashing load balancing algorithm if the primary hash
                  function fails.
                  Algorithm must be set to "consistent-hashing" for this field to have effect.
                properties:
                  cookie:
                    description: Cookie is the name of the cookie to use as hash input.
                    type: string
                  cookiePath:
                    description: CookiePath is cookie path to set in the response
                      headers.
                    type: string
                  header:
                    description: Header is the name of the header to use as hash input.
                    type: string
                  input:
                    description: |-
                      Input allows using one of the predefined inputs (ip, consumer, path, none).
                      Set this to `none` if you want to use sticky sessions.
                      For other parametrized inputs, use one of the fields below.
                    enum:
                    - ip
                    - consumer
                    - path
                    - none
                    type: string
                  queryArg:
                    description: QueryArg is the name of the query argument to use
                      as hash input.
                    type: string
                  uriCapture:
                    description: URICapture is the name of the URI capture group to
                      use as hash input.
                    type: string
                type: object
              healthchecks:
                description: Healthchecks defines the health check configurations
                  in Kong.
                properties:
                  active:
                    description: Active configures active health check probing.
                    properties:
                      concurrency:
                        description: Concurrency is the number of targets to check
                          concurrently.
                        minimum: 1
                        type: integer
                      headers:
                        additionalProperties:
                          items:
                            type: string
                          type: array
                        description: Headers is a list of HTTP headers to add to the
                          probe request.
                        type: object
                      healthy:
                        description: Healthy configures thresholds and HTTP status
                          codes to mark targets healthy for an upstream.
                        properties:
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a success.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in a healthy
                              state.
                            minimum: 0
                            type: integer
                          successes:
                            description: Successes is the number of successes to consider
                              a target healthy.
                            minimum: 0
                            type: integer
                        type: object
                      httpPath:
                        description: HTTPPath is the path to use in GET HTTP request
                          to run as a probe.
                        pattern: ^/.*$
                        type: string
                      httpsSni:
                        description: HTTPSSNI is the SNI to use in GET HTTPS request
                          to run as a probe.
                        type: string
                      httpsVerifyCertificate:
                        description: HTTPSVerifyCertificate is a boolean value that
                          indicates if the certificate should be verified.
                        type: boolean
                      timeout:
                        description: Timeout is the probe timeout in seconds.
                        minimum: 0
                        type: integer
                      type:
                        description: |-
                          Type determines whether to perform active health checks using HTTP or HTTPS, or just attempt a TCP connection.
                          Accepted values are "http", "https", "tcp", "grpc", "grpcs".
                        enum:
                        - http
                        - https
                        - tcp
                        - grpc
                        - grpcs
                        type: string
                      unhealthy:
                        description: Unhealthy configures thresholds and HTTP status
                          codes to mark targets unhealthy for an upstream.
                        properties:
                          httpFailures:
                            description: HTTPFailures is the number of failures to
                              consider a target unhealthy.
                            minimum: 0
                            type: integer
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a failure.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in an unhealthy
                              state.
                            minimum: 0
                            type: integer
                          tcpFailures:
                            description: TCPFailures is the number of TCP failures
                              in a row to consider a target unhealthy.
                            minimum: 0
                            type: integer
                          timeouts:
                            description: Timeouts is the number of timeouts in a row
                              to consider a target unhealthy.
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  passive:
                    description: Passive configures passive health check probing.
                    properties:
                      healthy:
                        description: Healthy configures thresholds and HTTP status
                          codes to mark targets healthy for an upstream.
                        properties:
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a success.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in a healthy
                              state.
                            minimum: 0
                            type: integer
                          successes:
                            description: Successes is the number of successes to consider
                              a target healthy.
                            minimum: 0
                            type: integer
                        type: object
                      type:
                        description: |-
                          Type determines whether to perform passive health checks interpreting HTTP/HTTPS statuses,
                          or just check for TCP connection success.
                          Accepted values are "http", "https", "tcp", "grpc", "grpcs".
                        enum:
                        - http
                        - https
                        - tcp
                        - grpc
                        - grpcs
                        type: string
                      unhealthy:
                        description: Unhealthy configures thresholds and HTTP status
                          codes to mark targets unhealthy.
                        properties:
                          httpFailures:
                            description: HTTPFailures is the number of failures to
                              consider a target unhealthy.
                            minimum: 0
                            type: integer
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a failure.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in an unhealthy
                              state.
                            minimum: 0
                            type: integer
                          tcpFailures:
                            description: TCPFailures is the number of TCP failures
                              in a row to consider a target unhealthy.
                            minimum: 0
                            type: integer
                          timeouts:
                            description: Timeouts is the number of timeouts in a row
                              to consider a target unhealthy.
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  threshold:
                    description: |-
                      Threshold is the minimum percentage of the upstream’s targets’ weight that must be available for the whole
                      upstream to be considered healthy.
                    type: integer
                type: object
              slots:
                description: |-
                  Slots is the number of slots in the load balancer algorithm.
                  If not set, the default value in Kong for the algorithm is used.
                maximum: 65536
                minimum: 10
                type: integer
              stickySessions:
                description: |-
                  StickySessions defines the sticky session configuration for the upstream.
                  When enabled, clients will be routed to the same backend target based on a cookie.
                  This requires Kong Enterprise Gateway and setting `hash_on` to `none`.
                properties:
                  cookie:
                    description: |-
                      Cookie is the name of the cookie to use for sticky sessions.
                      Kong will generate this cookie if it doesn't exist in the request.
                    minLength: 1
                    type: string
                  cookiePath:
                    default: /
                    description: CookiePath is the path to set in the cookie.
                    type: string
                required:
                - cookie
                type: object
            type: object
          status:
            description: Status defines the current state of KongUpstreamPolicy
            properties:
              ancestors:
                description: |-
                  Ancestors is a list of ancestor resources (usually Gateways) that are
                  associated with the policy, and the status of the policy with respect to
                  each ancestor. When this policy attaches to a parent, the controller that
                  manages the parent and the ancestors MUST add an entry to this list when
                  the controller first sees the policy and SHOULD update the entry as
                  appropriate when the relevant ancestor is modified.

                  Note that choosing the relevant ancestor is left to the Policy designers;
                  an important part of Policy design is designing the right object level at
                  which to namespace this status.

                  Note also that implementations MUST ONLY populate ancestor status for
                  the Ancestor resources they are responsible for. Implementations MUST
                  use the ControllerName field to uniquely identify the entries in this list
                  that they are responsible for.

                  Note that to achieve this, the list of PolicyAncestorStatus structs
                  MUST be treated as a map with a composite key, made up of the AncestorRef
                  and ControllerName fields combined.

                  A maximum of 16 ancestors will be represented in this list. An empty list
                  means the Policy is not relevant for any ancestors.

                  If this slice is full, implementations MUST NOT add further entries.
                  Instead they MUST consider the policy unimplementable and signal that
                  on any related resources such as the ancestor that would be referenced
                  here. For example, if this list was full on BackendTLSPolicy, no
                  additional Gateways would be able to reference the Service targeted by
                  the BackendTLSPolicy.
                items:
                  description: |-
                    PolicyAncestorStatus describes the status of a route with respect to an
                    associated Ancestor.

                    Ancestors refer to objects that are either the Target of a policy or above it
                    in terms of object hierarchy. For example, if a policy targets a Service, the
                    Policy's Ancestors are, in order, the Service, the HTTPRoute, the Gateway, and
                    the GatewayClass. Almost always, in this hierarchy, the Gateway will be the most
                    useful object to place Policy status on, so we recommend that implementations
                    SHOULD use Gateway as the PolicyAncestorStatus object unless the designers
                    have a _very_ good reason otherwise.

                    In the context of policy attachment, the Ancestor is used to distinguish which
                    resource results in a distinct application of this policy. For example, if a policy
                    targets a Service, it may have a distinct result per attached Gateway.

                    Policies targeting the same resource may have different effects depending on the
                    ancestors of those resources. For example, different Gateways targeting the same
                    Service may have different capabilities, especially if they have different underlying
                    implementations.

                    For example, in BackendTLSPolicy, the Policy attaches to a Service that is
                    used as a backend in a HTTPRoute that is itself attached to a Gateway.
                    In this case, the relevant object for status is the Gateway, and that is the
                    ancestor object referred to in this status.

                    Note that a parent is also an ancestor, so for objects where the parent is the
                    relevant object for status, this struct SHOULD still be used.

                    This struct is intended to be used in a slice that's effectively a map,
                    with a composite key made up of the AncestorRef and the ControllerName.
                  properties:
                    ancestorRef:
                      description: |-
                        AncestorRef corresponds with a ParentRef in the spec that this
                        PolicyAncestorStatus struct describes the status of.
                      properties:
                        group:
                          default: gateway.networking.k8s.io
                          description: |-
                            Group is the group of the referent.
                            When unspecified, "gateway.networking.k8s.io" is inferred.
                            To set the core API group (such as for a "Service" kind referent),
                            Group must be explicitly set to "" (empty string).

                            Support: Core
                          maxLength: 253
                          pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        kind:
                          default: Gateway
                          description: |-
                            Kind is kind of the referent.

                            There are two kinds of parent resources with "Core" support:

                            * Gateway (Gateway conformance profile)
                            * Service (Mesh conformance profile, ClusterIP Services only)

                            Support for other resources is Implementation-Specific.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                          type: string
                        name:
                          description: |-
                            Name is the name of the referent.

                            Support: Core
                          maxLength: 253
                          minLength: 1
                          type: string
                        namespace:
                          description: |-
                            Namespace is the namespace of the referent. When unspecified, this refers
                            to the local namespace of the Route.

                            Note that there are specific rules for ParentRefs which cross namespace
                            boundaries. Cross-namespace references are only valid if they are explicitly
                            allowed by something in the namespace they are referring to. For example:
                            Gateway has the AllowedRoutes field, and ReferenceGrant provides a
                            generic way to enable any other kind of cross-namespace reference.

                            <gateway:experimental:description>
                            ParentRefs from a Route to a Service in the same namespace are "producer"
                            routes, which apply default routing rules to inbound connections from
                            any namespace to the Service.

                            ParentRefs from a Route to a Service in a different namespace are
                            "consumer" routes, and these routing rules are only applied to outbound
                            connections originating from the same namespace as the Route, for which
                            the intended destination of the connections are a Service targeted as a
                            ParentRef of the Route.
                            </gateway:experimental:description>

                            Support: Core
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                          type: string
                        port:
                          description: |-
                            Port is the network port this Route targets. It can be interpreted
                            differently based on the type of parent resource.

                            When the parent resource is a Gateway, this targets all listeners
                            listening on the specified port that also support this kind of Route(and
                            select this Route). It's not recommended to set `Port` unless the
                            networking behaviors specified in a Route must apply to a specific port
                            as opposed to a listener(s) whose port(s) may be changed. When both Port
                            and SectionName are specified, the name and port of the selected listener
                            must match both specified values.

                            <gateway:experimental:description>
                            When the parent resource is a Service, this targets a specific port in the
                            Service spec. When both Port (experimental) and SectionName are specified,
                            the name and port of the selected port must match both specified values.
                            </gateway:experimental:description>

                            Implementations MAY choose to support other parent resources.
                            Implementations supporting other types of parent resources MUST clearly
                            document how/if Port is interpreted.

                            For the purpose of status, an attachment is considered successful as
                            long as the parent resource accepts it partially. For example, Gateway
                            listeners can restrict which Routes can attach to them by Route kind,
                            namespace, or hostname. If 1 of 2 Gateway listeners accept attachment
                            from the referencing Route, the Route MUST be considered successfully
                            attached. If no Gateway listeners accept attachment from this Route,
                            the Route MUST be considered detached from the Gateway.

                            Support: Extended
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                        sectionName:
                          description: |-
                            SectionName is the name of a section within the target resource. In the
                            following resources, SectionName is interpreted as the following:

                            * Gateway: Listener name. When both Port (experimental) and SectionName
                            are specified, the name and port of the selected listener must match
                            both specified values.
                            * Service: Port name. When both Port (experimental) and SectionName
                            are specified, the name and port of the selected listener must match
                            both specified values.

                            Implementations MAY choose to support attaching Routes to other resources.
                            If that is the case, they MUST clearly document how SectionName is
                            interpreted.

                            When unspecified (empty string), this will reference the entire resource.
                            For the purpose of status, an attachment is considered successful if at
                            least one section in the parent resource accepts it. For example, Gateway
                            listeners can restrict which Routes can attach to them by Route kind,
                            namespace, or hostname. If 1 of 2 Gateway listeners accept attachment from
                            the referencing Route, the Route MUST be considered successfully
                            attached. If no Gateway listeners accept attachment from this Route, the
                            Route MUST be considered detached from the Gateway.

                            Support: Core
                          maxLength: 253
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                      required:
                      - name
                      type: object
                    conditions:
                      description: Conditions describes the status of the Policy with
                        respect to the given Ancestor.
                      items:
                        description: Condition contains details for one aspect of
                          the current state of this API Resource.
                        properties:
                          lastTransitionTime:
                            description: |-
                              lastTransitionTime is the last time the condition transitioned from one status to another.
                              This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                            format: date-time
                            type: string
                          message:
                            description: |-
                              message is a human readable message indicating details about the transition.
                              This may be an empty string.
                            maxLength: 32768
                            type: string
                          observedGeneration:
                            description: |-
                              observedGeneration represents the .metadata.generation that the condition was set based upon.
                              For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                              with respect to the current state of the instance.
                            format: int64
                            minimum: 0
                            type: integer
                          reason:
                            description: |-
                              reason contains a programmatic identifier indicating the reason for the condition's last transition.
                              Producers of specific condition types may define expected values and meanings for this field,
                              and whether the values are considered a guaranteed API.
                              The value should be a CamelCase string.
                              This field may not be empty.
                            maxLength: 1024
                            minLength: 1
                            pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                            type: string
                          status:
                            description: status of the condition, one of True, False,
                              Unknown.
                            enum:
                            - "True"
                            - "False"
                            - Unknown
                            type: string
                          type:
                            description: type of condition in CamelCase or in foo.example.com/CamelCase.
                            maxLength: 316
                            pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                            type: string
                        required:
                        - lastTransitionTime
                        - message
                        - reason
                        - status
                        - type
                        type: object
                      maxItems: 8
                      minItems: 1
                      type: array
                      x-kubernetes-list-map-keys:
                      - type
                      x-kubernetes-list-type: map
                    controllerName:
                      description: |-
                        ControllerName is a domain/path string that indicates the name of the
                        controller that wrote this status. This corresponds with the
                        controllerName field on GatewayClass.

                        Example: "example.net/gateway-controller".

                        The format of this field is DOMAIN "/" PATH, where DOMAIN and PATH are
                        valid Kubernetes names
                        (https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names).

                        Controllers MUST populate this field when writing status. Controllers should ensure that
                        entries to status populated with their ControllerName are cleaned up when they are no
                        longer necessary.
                      maxLength: 253
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*\/[A-Za-z0-9\/\-._~%!$&'()*+,;=:]+$
                      type: string
                  required:
                  - ancestorRef
                  - controllerName
                  type: object
                maxItems: 16
                type: array
            required:
            - ancestors
            type: object
        type: object
        x-kubernetes-validations:
        - message: Only one of spec.hashOn.(input|cookie|header|uriCapture|queryArg)
            can be set.
          rule: 'has(self.spec.hashOn) ? [has(self.spec.hashOn.input), has(self.spec.hashOn.cookie),
            has(self.spec.hashOn.header), has(self.spec.hashOn.uriCapture), has(self.spec.hashOn.queryArg)].filter(fieldSet,
            fieldSet == true).size() <= 1 : true'
        - message: When spec.hashOn.cookie is set, spec.hashOn.cookiePath is required.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookie) ? has(self.spec.hashOn.cookiePath)
            : true'
        - message: When spec.hashOn.cookiePath is set, spec.hashOn.cookie is required.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookiePath) ? has(self.spec.hashOn.cookie)
            : true'
        - message: spec.algorithm must be set to "consistent-hashing" when spec.hashOn
            is set.
          rule: 'has(self.spec.hashOn) ? has(self.spec.algorithm) && self.spec.algorithm
            == "consistent-hashing" : true'
        - message: Only one of spec.hashOnFallback.(input|header|uriCapture|queryArg)
            can be set.
          rule: 'has(self.spec.hashOnFallback) ? [has(self.spec.hashOnFallback.input),
            has(self.spec.hashOnFallback.header), has(self.spec.hashOnFallback.uriCapture),
            has(self.spec.hashOnFallback.queryArg)].filter(fieldSet, fieldSet == true).size()
            <= 1 : true'
        - message: spec.algorithm must be set to "consistent-hashing" when spec.hashOnFallback
            is set.
          rule: 'has(self.spec.hashOnFallback) ? has(self.spec.algorithm) && self.spec.algorithm
            == "consistent-hashing" : true'
        - message: spec.hashOnFallback.cookie must not be set.
          rule: 'has(self.spec.hashOnFallback) ? !has(self.spec.hashOnFallback.cookie)
            : true'
        - message: spec.hashOnFallback.cookiePath must not be set.
          rule: 'has(self.spec.hashOnFallback) ? !has(self.spec.hashOnFallback.cookiePath)
            : true'
        - message: spec.healthchecks.passive.healthy.interval must not be set.
          rule: 'has(self.spec.healthchecks) && has(self.spec.healthchecks.passive)
            && has(self.spec.healthchecks.passive.healthy) ? !has(self.spec.healthchecks.passive.healthy.interval)
            : true'
        - message: spec.healthchecks.passive.unhealthy.interval must not be set.
          rule: 'has(self.spec.healthchecks) && has(self.spec.healthchecks.passive)
            && has(self.spec.healthchecks.passive.unhealthy) ? !has(self.spec.healthchecks.passive.unhealthy.interval)
            : true'
        - message: spec.hashOnFallback must not be set when spec.hashOn.cookie is
            set.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookie) ? !has(self.spec.hashOnFallback)
            : true'
        - message: When spec.stickySessions is set, spec.hashOn.input must be set
            to 'none' (no other hash_on fields allowed).
          rule: 'has(self.spec.stickySessions) ? (has(self.spec.hashOn) && has(self.spec.hashOn.input)
            && self.spec.hashOn.input == ''none'' && !has(self.spec.hashOn.cookie)
            && !has(self.spec.hashOn.cookiePath) && !has(self.spec.hashOn.header)
            && !has(self.spec.hashOn.uriCapture) && !has(self.spec.hashOn.queryArg))
            : true'
        - message: spec.stickySessions.cookie is required when spec.stickySessions
            is set.
          rule: 'has(self.spec.stickySessions) ? has(self.spec.stickySessions.cookie)
            : true'
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller,gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongvaults.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongVault
    listKind: KongVaultList
    plural: kongvaults
    shortNames:
    - kv
    singular: kongvault
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Name of the backend of the vault
      jsonPath: .spec.backend
      name: Backend Type
      type: string
    - description: Prefix of vault URI to reference the values in the vault
      jsonPath: .spec.prefix
      name: Prefix
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Description
      jsonPath: .spec.description
      name: Description
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          KongVault is the schema for kongvaults API which defines a custom Kong vault.
          A Kong vault is a storage to store sensitive data, where the values can be referenced in configuration of plugins.
          See: https://docs.konghq.com/gateway/latest/kong-enterprise/secrets-management/
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongVaultSpec defines specification of a custom Kong vault.
            properties:
              backend:
                description: |-
                  Backend is the type of the backend storing the secrets in the vault.
                  The supported backends of Kong is listed here:
                  https://docs.konghq.com/gateway/latest/kong-enterprise/secrets-management/backends/
                minLength: 1
                type: string
              config:
                description: Config is the configuration of the vault. Varies for
                  different backends.
                x-kubernetes-preserve-unknown-fields: true
              controlPlaneRef:
                description: ControlPlaneRef is a reference to a Konnect ControlPlane
                  this KongVault is associated with.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                    type: string
                  konnectNamespacedRef:
                    description: |-
                      KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                      It contains the name of the Konnect Control Plane.
                      This field is required when the Type is konnectNamespacedRef.
                    properties:
                      name:
                        description: Name is the name of the Konnect Control Plane.
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace where the Konnect Control Plane is in.
                          Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    default: kic
                    description: |-
                      Type indicates the type of the control plane being referenced. Allowed values:
                      - konnectID
                      - konnectNamespacedRef
                      - kic

                      The default is kic, which implies that the Control Plane is KIC.
                    enum:
                    - konnectID
                    - konnectNamespacedRef
                    - kic
                    type: string
                type: object
                x-kubernetes-validations:
                - message: '''konnectID'' type is not supported'
                  rule: self.type != 'konnectID'
                - message: when type is konnectNamespacedRef, konnectNamespacedRef
                    must be set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? has(self.konnectNamespacedRef) : true'
                - message: when type is konnectNamespacedRef, konnectID must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? has(self.konnectID)
                    : true'
                - message: when type is konnectID, konnectNamespacedRef must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is kic, konnectID must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                    : true'
                - message: when type is kic, konnectNamespacedRef must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is unset, konnectID must not be set
                  rule: '!has(self.type) ? !has(self.konnectID) : true'
                - message: when type is unset, konnectNamespacedRef must not be set
                  rule: '!has(self.type) ? !has(self.konnectNamespacedRef) : true'
              description:
                description: Description is the additional information about the vault.
                type: string
              prefix:
                description: |-
                  Prefix is the prefix of vault URI for referencing values in the vault.
                  It is immutable after created.
                minLength: 1
                type: string
              tags:
                description: Tags are the tags associated to the vault for grouping
                  and filtering.
                items:
                  type: string
                maxItems: 20
                type: array
                x-kubernetes-validations:
                - message: tags entries must not be longer than 128 characters
                  rule: self.all(tag, size(tag) >= 1 && size(tag) <= 128)
            required:
            - backend
            - prefix
            type: object
          status:
            description: KongVaultStatus represents the current status of the KongVault
              resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KongVaultStatus.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              konnect:
                description: Konnect contains the Konnect entity status.
                properties:
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this Route is associated with.
                    type: string
                  id:
                    description: |-
                      ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                      If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                    type: string
                  organizationID:
                    description: OrgID is ID of Konnect Org that this entity has been
                      created in.
                    type: string
                  serverURL:
                    description: ServerURL is the URL of the Konnect server in which
                      the entity exists.
                    type: string
                type: object
            required:
            - conditions
            type: object
        required:
        - spec
        type: object
        x-kubernetes-validations:
        - message: The spec.prefix field is immutable
          rule: self.spec.prefix == oldSelf.spec.prefix
        - message: controlPlaneRef is required once set
          rule: '!has(oldSelf.spec.controlPlaneRef) || has(self.spec.controlPlaneRef)'
        - message: spec.controlPlaneRef is immutable when an entity is already Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'') || !has(self.spec.controlPlaneRef))
            ? true : oldSelf.spec.controlPlaneRef == self.spec.controlPlaneRef'
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: tcpingresses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: TCPIngress
    listKind: TCPIngressList
    plural: tcpingresses
    singular: tcpingress
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Address of the load balancer
      jsonPath: .status.loadBalancer.ingress[*].ip
      name: Address
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    deprecated: true
    deprecationWarning: configuration.konghq.com/v1beta1 TCPIngress is deprecated
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: |-
          TCPIngress is the Schema for the tcpingresses API.
          Deprecated: Use Gateway API instead.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the TCPIngress specification.
            properties:
              rules:
                description: A list of rules used to configure the Ingress.
                items:
                  description: |-
                    IngressRule represents a rule to apply against incoming requests.
                    Matching is performed based on an (optional) SNI and port.
                  properties:
                    backend:
                      description: |-
                        Backend defines the referenced service endpoint to which the traffic
                        will be forwarded to.
                      properties:
                        serviceName:
                          description: Specifies the name of the referenced service.
                          minLength: 1
                          type: string
                        servicePort:
                          description: Specifies the port of the referenced service.
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - serviceName
                      - servicePort
                      type: object
                    host:
                      description: |-
                        Host is the fully qualified domain name of a network host, as defined
                        by RFC 3986.
                        If a Host is not specified, then port-based TCP routing is performed. Kong
                        doesn't care about the content of the TCP stream in this case.
                        If a Host is specified, the protocol must be TLS over TCP.
                        A plain-text TCP request cannot be routed based on Host. It can only
                        be routed based on Port.
                      type: string
                    port:
                      description: |-
                        Port is the port on which to accept TCP or TLS over TCP sessions and
                        route. It is a required field. If a Host is not specified, the requested
                        are routed based only on Port.
                      format: int32
                      maximum: 65535
                      minimum: 1
                      type: integer
                  required:
                  - backend
                  - port
                  type: object
                type: array
              tls:
                description: |-
                  TLS configuration. This is similar to the `tls` section in the
                  Ingress resource in networking.v1beta1 group.
                  The mapping of SNIs to TLS cert-key pair defined here will be
                  used for HTTP Ingress rules as well. Once can define the mapping in
                  this resource or the original Ingress resource, both have the same
                  effect.
                items:
                  description: IngressTLS describes the transport layer security.
                  properties:
                    hosts:
                      description: |-
                        Hosts are a list of hosts included in the TLS certificate. The values in
                        this list must match the name/s used in the tlsSecret. Defaults to the
                        wildcard host setting for the loadbalancer controller fulfilling this
                        Ingress, if left unspecified.
                      items:
                        type: string
                      type: array
                    secretName:
                      description: SecretName is the name of the secret used to terminate
                        SSL traffic.
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: TCPIngressStatus defines the observed state of TCPIngress.
            properties:
              loadBalancer:
                description: LoadBalancer contains the current status of the load-balancer.
                properties:
                  ingress:
                    description: |-
                      Ingress is a list containing ingress points for the load-balancer.
                      Traffic intended for the service should be sent to these ingress points.
                    items:
                      description: |-
                        LoadBalancerIngress represents the status of a load-balancer ingress point:
                        traffic intended for the service should be sent to an ingress point.
                      properties:
                        hostname:
                          description: |-
                            Hostname is set for load-balancer ingress points that are DNS based
                            (typically AWS load-balancers)
                          type: string
                        ip:
                          description: |-
                            IP is set for load-balancer ingress points that are IP based
                            (typically GCE or OpenStack load-balancers)
                          type: string
                        ipMode:
                          description: |-
                            IPMode specifies how the load-balancer IP behaves, and may only be specified when the ip field is specified.
                            Setting this to "VIP" indicates that traffic is delivered to the node with
                            the destination set to the load-balancer's IP and port.
                            Setting this to "Proxy" indicates that traffic is delivered to the node or pod with
                            the destination set to the node's IP and node port or the pod's IP and port.
                            Service implementations may use this information to adjust traffic routing.
                          type: string
                        ports:
                          description: |-
                            Ports is a list of records of service ports
                            If used, every port defined in the service should have an entry in it
                          items:
                            description: PortStatus represents the error condition
                              of a service port
                            properties:
                              error:
                                description: |-
                                  Error is to record the problem with the service port
                                  The format of the error shall comply with the following rules:
                                  - built-in error values shall be specified in this file and those shall use
                                    CamelCase names
                                  - cloud provider specific error values must have names that comply with the
                                    format foo.example.com/CamelCase.
                                maxLength: 316
                                pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                                type: string
                              port:
                                description: Port is the port number of the service
                                  port of which status is recorded here
                                format: int32
                                type: integer
                              protocol:
                                description: |-
                                  Protocol is the protocol of the service port of which status is recorded here
                                  The supported values are: "TCP", "UDP", "SCTP"
                                type: string
                            required:
                            - error
                            - port
                            - protocol
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: udpingresses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: UDPIngress
    listKind: UDPIngressList
    plural: udpingresses
    singular: udpingress
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Address of the load balancer
      jsonPath: .status.loadBalancer.ingress[*].ip
      name: Address
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    deprecated: true
    deprecationWarning: configuration.konghq.com/v1beta1 UDPIngress is deprecated
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: |-
          UDPIngress is the Schema for the udpingresses API.
          Deprecated: Use Gateway API instead.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the UDPIngress specification.
            properties:
              rules:
                description: A list of rules used to configure the Ingress.
                items:
                  description: |-
                    UDPIngressRule represents a rule to apply against incoming requests
                    wherein no Host matching is available for request routing, only the port
                    is used to match requests.
                  properties:
                    backend:
                      description: |-
                        Backend defines the Kubernetes service which accepts traffic from the
                        listening Port defined above.
                      properties:
                        serviceName:
                          description: Specifies the name of the referenced service.
                          minLength: 1
                          type: string
                        servicePort:
                          description: Specifies the port of the referenced service.
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - serviceName
                      - servicePort
                      type: object
                    port:
                      description: |-
                        Port indicates the port for the Kong proxy to accept incoming traffic
                        on, which will then be routed to the service Backend.
                      format: int32
                      maximum: 65535
                      minimum: 1
                      type: integer
                  required:
                  - backend
                  - port
                  type: object
                type: array
            type: object
          status:
            description: UDPIngressStatus defines the observed state of UDPIngress.
            properties:
              loadBalancer:
                description: LoadBalancer contains the current status of the load-balancer.
                properties:
                  ingress:
                    description: |-
                      Ingress is a list containing ingress points for the load-balancer.
                      Traffic intended for the service should be sent to these ingress points.
                    items:
                      description: |-
                        LoadBalancerIngress represents the status of a load-balancer ingress point:
                        traffic intended for the service should be sent to an ingress point.
                      properties:
                        hostname:
                          description: |-
                            Hostname is set for load-balancer ingress points that are DNS based
                            (typically AWS load-balancers)
                          type: string
                        ip:
                          description: |-
                            IP is set for load-balancer ingress points that are IP based
                            (typically GCE or OpenStack load-balancers)
                          type: string
                        ipMode:
                          description: |-
                            IPMode specifies how the load-balancer IP behaves, and may only be specified when the ip field is specified.
                            Setting this to "VIP" indicates that traffic is delivered to the node with
                            the destination set to the load-balancer's IP and port.
                            Setting this to "Proxy" indicates that traffic is delivered to the node or pod with
                            the destination set to the node's IP and node port or the pod's IP and port.
                            Service implementations may use this information to adjust traffic routing.
                          type: string
                        ports:
                          description: |-
                            Ports is a list of records of service ports
                            If used, every port defined in the service should have an entry in it
                          items:
                            description: PortStatus represents the error condition
                              of a service port
                            properties:
                              error:
                                description: |-
                                  Error is to record the problem with the service port
                                  The format of the error shall comply with the following rules:
                                  - built-in error values shall be specified in this file and those shall use
                                    CamelCase names
                                  - cloud provider specific error values must have names that comply with the
                                    format foo.example.com/CamelCase.
                                maxLength: 316
                                pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                                type: string
                              port:
                                description: Port is the port number of the service
                                  port of which status is recorded here
                                format: int32
                                type: integer
                              protocol:
                                description: |-
                                  Protocol is the protocol of the service port of which status is recorded here
                                  The supported values are: "TCP", "UDP", "SCTP"
                                type: string
                            required:
                            - error
                            - port
                            - protocol
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kong-leader-election
  namespace: kong
rules:
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kong-ingress
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - nodes
  verbs:
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - pods
  - services
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - create
  - get
  - list
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - services/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - ingressclassparameterses
  - kongclusterplugins
  - kongconsumergroups
  - kongconsumers
  - kongcustomentities
  - kongingresses
  - konglicenses
  - kongplugins
  - kongupstreampolicies
  - kongvaults
  - tcpingresses
  - udpingresses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongclusterplugins/status
  - kongconsumergroups/status
  - kongconsumers/status
  - kongcustomentities/status
  - kongingresses/status
  - konglicenses/status
  - kongplugins/status
  - kongupstreampolicies/status
  - kongvaults/status
  - tcpingresses/status
  - udpingresses/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - discovery.k8s.io
  resources:
  - endpointslices
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - httproutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - incubator.ingress-controller.konghq.com
  resources:
  - kongservicefacades
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - incubator.ingress-controller.konghq.com
  resources:
  - kongservicefacades/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - networking.k8s.io
  resources:
  - ingressclasses
  - ingresses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kong-ingress-crds
rules:
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kong-ingress-gateway
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - namespaces
  - secrets
  - services
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - backendtlspolicies
  - gatewayclasses
  - grpcroutes
  - httproutes
  - referencegrants
  - tcproutes
  - tlsroutes
  - udproutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - backendtlspolicies/status
  verbs:
  - patch
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gatewayclasses/status
  - gateways/status
  - httproutes/status
  - tcproutes/status
  - tlsroutes/status
  - udproutes/status
  verbs:
  - get
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gateways
  verbs:
  - get
  - list
  - update
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - grpcroutes/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - referencegrants/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kong-leader-election
  namespace: kong
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kong-leader-election
subjects:
- kind: ServiceAccount
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kong-ingress
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kong-ingress
subjects:
- kind: ServiceAccount
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kong-ingress-crds
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kong-ingress-crds
subjects:
- kind: ServiceAccount
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kong-ingress-gateway
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kong-ingress-gateway
subjects:
- kind: ServiceAccount
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: v1
kind: Service
metadata:
  name: kong-admin
  namespace: kong
spec:
  externalTrafficPolicy: Local
  ports:
  - name: admin
    port: 80
    protocol: TCP
    targetPort: 8001
  selector:
    app: ingress-kong
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: kong-manager
  namespace: kong
spec:
  externalTrafficPolicy: Local
  ports:
  - name: manager
    port: 80
    protocol: TCP
    targetPort: 8002
  selector:
    app: ingress-kong
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
  name: kong-proxy
  namespace: kong
spec:
  ports:
  - name: proxy
    port: 80
    protocol: TCP
    targetPort: 8000
  - name: proxy-ssl
    port: 443
    protocol: TCP
    targetPort: 8443
  selector:
    app: ingress-kong
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: kong-validation-webhook
  namespace: kong
spec:
  ports:
  - name: webhook
    port: 443
    protocol: TCP
    targetPort: 8080
  selector:
    app: ingress-kong
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: kong
spec:
  ports:
  - name: pgql
    port: 5432
    protocol: TCP
    targetPort: 5432
  selector:
    app: postgres
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ingress-kong
  name: ingress-kong
  namespace: kong
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ingress-kong
  template:
    metadata:
      annotations:
        kuma.io/gateway: enabled
        kuma.io/service-account-token-volume: kong-serviceaccount-token
        traffic.sidecar.istio.io/includeInboundPorts: ""
      labels:
        app: ingress-kong
    spec:
      automountServiceAccountToken: false
      containers:
      - env:
        - name: KONG_LICENSE_DATA
          valueFrom:
            secretKeyRef:
              key: license
              name: kong-enterprise-license
        - name: KONG_ADMIN_API_URI
          value: set-me
        - name: KONG_ADMIN_GUI_AUTH
          value: basic-auth
        - name: KONG_ENFORCE_RBAC
          value: "on"
        - name: KONG_ADMIN_GUI_SESSION_CONF
          value: '{"cookie_secure":false,"storage":"kong","cookie_name":"admin_session","cookie_lifetime":********,"cookie_samesite":"off","secret":"please-change-me"}'
        - name: KONG_ADMIN_LISTEN
          value: 0.0.0.0:8001, 0.0.0.0:8444 ssl
        - name: KONG_ROUTER_FLAVOR
          value: traditional_compatible
        - name: KONG_DATABASE
          value: postgres
        - name: KONG_PG_HOST
          value: postgres
        - name: KONG_PG_PASSWORD
          value: kong
        - name: KONG_PROXY_LISTEN
          value: 0.0.0.0:8000 reuseport backlog=16384, 0.0.0.0:8443 http2 ssl reuseport
            backlog=16384
        - name: KONG_PORT_MAPS
          value: 80:8000, 443:8443
        - name: KONG_STATUS_LISTEN
          value: 0.0.0.0:8100
        - name: KONG_NGINX_WORKER_PROCESSES
          value: "2"
        - name: KONG_KIC
          value: "on"
        - name: KONG_ADMIN_ACCESS_LOG
          value: /dev/stdout
        - name: KONG_ADMIN_ERROR_LOG
          value: /dev/stderr
        - name: KONG_PROXY_ERROR_LOG
          value: /dev/stderr
        image: kong/kong-gateway:3.10
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/bash
              - -c
              - kong quit
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /status
            port: 8100
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        name: proxy
        ports:
        - containerPort: 8001
          name: admin
          protocol: TCP
        - containerPort: 8002
          name: manager
          protocol: TCP
        - containerPort: 8000
          name: proxy
          protocol: TCP
        - containerPort: 8443
          name: proxy-ssl
          protocol: TCP
        - containerPort: 8100
          name: metrics
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /status
            port: 8100
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
      - env:
        - name: CONTROLLER_KONG_ADMIN_TOKEN
          valueFrom:
            secretKeyRef:
              key: password
              name: kong-enterprise-superuser-password
        - name: CONTROLLER_KONG_ADMIN_URL
          value: https://127.0.0.1:8444
        - name: CONTROLLER_KONG_ADMIN_TLS_SKIP_VERIFY
          value: "true"
        - name: CONTROLLER_PUBLISH_SERVICE
          value: kong/kong-proxy
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        image: kong/kubernetes-ingress-controller:3.4
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 10254
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        name: ingress-controller
        ports:
        - containerPort: 8080
          name: webhook
          protocol: TCP
        - containerPort: 10255
          name: cmetrics
          protocol: TCP
        - containerPort: 10256
          name: diagnostics
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /readyz
            port: 10254
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
          name: kong-serviceaccount-token
          readOnly: true
      imagePullSecrets:
      - name: kong-enterprise-edition-docker
      initContainers:
      - command:
        - /bin/bash
        - -c
        - while true; do kong migrations list; if [[ 0 -eq $? ]]; then exit 0; fi;
          sleep 2;  done;
        env:
        - name: KONG_LICENSE_DATA
          valueFrom:
            secretKeyRef:
              key: license
              name: kong-enterprise-license
        - name: KONG_PG_HOST
          value: postgres
        - name: KONG_PG_PASSWORD
          value: kong
        image: kong/kong-gateway:3.10
        name: wait-for-migrations
      serviceAccountName: kong-serviceaccount
      volumes:
      - name: kong-serviceaccount-token
        projected:
          sources:
          - serviceAccountToken:
              expirationSeconds: 3607
              path: token
          - configMap:
              items:
              - key: ca.crt
                path: ca.crt
              name: kube-root-ca.crt
          - downwardAPI:
              items:
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
                path: namespace
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: kong
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  serviceName: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - env:
        - name: POSTGRES_USER
          value: kong
        - name: POSTGRES_PASSWORD
          value: kong
        - name: POSTGRES_DB
          value: kong
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        image: postgres:9.5
        name: postgres
        ports:
        - containerPort: 5432
        volumeMounts:
        - mountPath: /var/lib/postgresql/data
          name: datadir
          subPath: pgdata
      terminationGracePeriodSeconds: 60
  volumeClaimTemplates:
  - metadata:
      name: datadir
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 1Gi
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kong-migrations
  namespace: kong
spec:
  template:
    metadata:
      name: kong-migrations
    spec:
      containers:
      - command:
        - /bin/bash
        - -c
        - kong migrations bootstrap && kong migrations up && kong migrations finish
        env:
        - name: KONG_LICENSE_DATA
          valueFrom:
            secretKeyRef:
              key: license
              name: kong-enterprise-license
        - name: KONG_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: kong-enterprise-superuser-password
        - name: KONG_PG_PASSWORD
          value: kong
        - name: KONG_PG_HOST
          value: postgres
        - name: KONG_PG_PORT
          value: "5432"
        image: kong/kong-gateway:3.10
        name: kong-migrations
      imagePullSecrets:
      - name: kong-enterprise-edition-docker
      initContainers:
      - command:
        - /bin/bash
        - -c
        - until timeout 1 bash 9<>/dev/tcp/${KONG_PG_HOST}/${KONG_PG_PORT}; do echo
          'waiting for db'; sleep 1; done
        env:
        - name: KONG_PG_HOST
          value: postgres
        - name: KONG_PG_PORT
          value: "5432"
        image: kong/kong-gateway:3.10
        name: wait-for-postgres
      restartPolicy: OnFailure
---
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: kong
spec:
  controller: ingress-controllers.konghq.com/kong
