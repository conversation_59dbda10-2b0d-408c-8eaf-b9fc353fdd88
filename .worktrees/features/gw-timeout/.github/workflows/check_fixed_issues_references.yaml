name: check fixed issues references (nightly)

on:
  schedule:
    - cron: '30 4 * * *'
  workflow_dispatch: {}

jobs:
    check_issues_state:
      timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
      runs-on: ubuntu-latest
      permissions:
        issues: read
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      steps:
        - name: checkout repository
          uses: actions/checkout@v4
        - name: check issues
          run: ./hack/check_fixed_issues_references.sh

    notify-on-slack:
      timeout-minutes: ${{ fromJSON(vars.GHA_DEFAULT_TIMEOUT) }}
      runs-on: ubuntu-latest
      needs:
        - check_issues_state
      if: always() && contains(needs.*.result, 'failure') && github.event_name == 'schedule'
      steps:
        - name: Notify on Slack for failures of checking issues state run automatically at night
          uses: 8398a7/action-slack@v3
          env:
            SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          with:
            # Since notify-on-slack is triggered on failure (if statement), we can hardcode this.
            status: failure
            fields: repo,message,commit,author,action,eventName,ref,workflow
            text: ':github: Some references to fixed issues exist in the codebase.'
