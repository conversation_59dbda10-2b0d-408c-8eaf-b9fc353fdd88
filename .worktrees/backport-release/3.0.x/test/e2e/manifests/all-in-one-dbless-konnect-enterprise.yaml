# Generated by build-single-manifest.sh. NOT FOR PRODUCTION USE (only used internally for testing). DO NOT EDIT.

apiVersion: v1
kind: Namespace
metadata:
  name: kong
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: ingressclassparameterses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    kind: IngressClassParameters
    listKind: IngressClassParametersList
    plural: ingressclassparameterses
    singular: ingressclassparameters
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: IngressClassParameters is the Schema for the IngressClassParameters
          API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the IngressClassParameters specification.
            properties:
              enableLegacyRegexDetection:
                default: false
                description: EnableLegacyRegexDetection automatically detects if ImplementationSpecific
                  Ingress paths are regular expression paths using the legacy 2.x
                  heuristic. The controller adds the "~" prefix to those paths if
                  the Kong version is 3.0 or higher.
                type: boolean
              serviceUpstream:
                default: false
                description: Offload load-balancing to kube-proxy or sidecar.
                type: boolean
            type: object
        type: object
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: kongclusterplugins.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongClusterPlugin
    listKind: KongClusterPluginList
    plural: kongclusterplugins
    shortNames:
    - kcp
    singular: kongclusterplugin
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Name of the plugin
      jsonPath: .plugin
      name: Plugin-Type
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Indicates if the plugin is disabled
      jsonPath: .disabled
      name: Disabled
      priority: 1
      type: boolean
    - description: Configuration of the plugin
      jsonPath: .config
      name: Config
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: KongClusterPlugin is the Schema for the kongclusterplugins API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          config:
            description: Config contains the plugin configuration. It's a list of
              keys and values required to configure the plugin. Please read the documentation
              of the plugin being configured to set values in here. For any plugin
              in Kong, anything that goes in the `config` JSON key in the Admin API
              request, goes into this property. Only one of `config` or `configFrom`
              may be used in a KongClusterPlugin, not both at once.
            type: object
            x-kubernetes-preserve-unknown-fields: true
          configFrom:
            description: ConfigFrom references a secret containing the plugin configuration.
              This should be used when the plugin configuration contains sensitive
              information, such as AWS credentials in the Lambda plugin or the client
              secret in the OIDC plugin. Only one of `config` or `configFrom` may
              be used in a KongClusterPlugin, not both at once.
            properties:
              secretKeyRef:
                description: Specifies a name, a namespace, and a key of a secret
                  to refer to.
                properties:
                  key:
                    description: The key containing the value.
                    type: string
                  name:
                    description: The secret containing the key.
                    type: string
                  namespace:
                    description: The namespace containing the secret.
                    type: string
                required:
                - key
                - name
                - namespace
                type: object
            type: object
          consumerRef:
            description: ConsumerRef is a reference to a particular consumer.
            type: string
          disabled:
            description: Disabled set if the plugin is disabled or not.
            type: boolean
          instance_name:
            description: InstanceName is an optional custom name to identify an instance
              of the plugin. This is useful when running the same plugin in multiple
              contexts, for example, on multiple services.
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          ordering:
            description: 'Ordering overrides the normal plugin execution order. It''s
              only available on Kong Enterprise. `<phase>` is a request processing
              phase (for example, `access` or `body_filter`) and `<plugin>` is the
              name of the plugin that will run before or after the KongPlugin. For
              example, a KongPlugin with `plugin: rate-limiting` and `before.access:
              ["key-auth"]` will create a rate limiting plugin that limits requests
              _before_ they are authenticated.'
            properties:
              after:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
              before:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
            type: object
          plugin:
            description: PluginName is the name of the plugin to which to apply the
              config.
            type: string
          protocols:
            description: Protocols configures plugin to run on requests received on
              specific protocols.
            items:
              description: KongProtocol is a valid Kong protocol. This alias is necessary
                to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342
              enum:
              - http
              - https
              - grpc
              - grpcs
              - tcp
              - tls
              - udp
              type: string
            type: array
          run_on:
            description: RunOn configures the plugin to run on the first or the second
              or both nodes in case of a service mesh deployment.
            enum:
            - first
            - second
            - all
            type: string
          status:
            description: Status represents the current status of the KongClusterPlugin
              resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: "Conditions describe the current conditions of the KongClusterPluginStatus.
                  \n Known condition types are: \n * \"Programmed\""
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - plugin
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: kongconsumergroups.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongConsumerGroup
    listKind: KongConsumerGroupList
    plural: kongconsumergroups
    shortNames:
    - kcg
    singular: kongconsumergroup
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: KongConsumerGroup is the Schema for the kongconsumergroups API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          status:
            description: Status represents the current status of the KongConsumer
              resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: "Conditions describe the current conditions of the KongConsumerGroup.
                  \n Known condition types are: \n * \"Programmed\""
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: kongconsumers.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongConsumer
    listKind: KongConsumerList
    plural: kongconsumers
    shortNames:
    - kc
    singular: kongconsumer
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Username of a Kong Consumer
      jsonPath: .username
      name: Username
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: KongConsumer is the Schema for the kongconsumers API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          consumerGroups:
            description: ConsumerGroups are references to consumer groups (that consumer
              wants to be part of) provisioned in Kong.
            items:
              type: string
            type: array
          credentials:
            description: Credentials are references to secrets containing a credential
              to be provisioned in Kong.
            items:
              type: string
            type: array
          custom_id:
            description: CustomID is a Kong cluster-unique existing ID for the consumer
              - useful for mapping Kong with users in your existing database.
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          status:
            description: Status represents the current status of the KongConsumer
              resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: "Conditions describe the current conditions of the KongConsumer.
                  \n Known condition types are: \n * \"Programmed\""
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
          username:
            description: Username is a Kong cluster-unique username of the consumer.
            type: string
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: kongingresses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongIngress
    listKind: KongIngressList
    plural: kongingresses
    shortNames:
    - ki
    singular: kongingress
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: KongIngress is the Schema for the kongingresses API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          proxy:
            description: Proxy defines additional connection options for the routes
              to be configured in the Kong Gateway, e.g. `connection_timeout`, `retries`,
              etc.
            properties:
              connect_timeout:
                description: "The timeout in milliseconds for\testablishing a connection
                  to the upstream server. Deprecated: use Service's \"konghq.com/connect-timeout\"
                  annotation instead."
                minimum: 0
                type: integer
              path:
                description: '(optional) The path to be used in requests to the upstream
                  server. Deprecated: use Service''s "konghq.com/path" annotation
                  instead.'
                pattern: ^/.*$
                type: string
              protocol:
                description: 'The protocol used to communicate with the upstream.
                  Deprecated: use Service''s "konghq.com/protocol" annotation instead.'
                enum:
                - http
                - https
                - grpc
                - grpcs
                - tcp
                - tls
                - udp
                type: string
              read_timeout:
                description: 'The timeout in milliseconds between two successive read
                  operations for transmitting a request to the upstream server. Deprecated:
                  use Service''s "konghq.com/read-timeout" annotation instead.'
                minimum: 0
                type: integer
              retries:
                description: 'The number of retries to execute upon failure to proxy.
                  Deprecated: use Service''s "konghq.com/retries" annotation instead.'
                minimum: 0
                type: integer
              write_timeout:
                description: 'The timeout in milliseconds between two successive write
                  operations for transmitting a request to the upstream server. Deprecated:
                  use Service''s "konghq.com/write-timeout" annotation instead.'
                minimum: 0
                type: integer
            type: object
          route:
            description: Route define rules to match client requests. Each Route is
              associated with a Service, and a Service may have multiple Routes associated
              to it.
            properties:
              headers:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: 'Headers contains one or more lists of values indexed
                  by header name that will cause this Route to match if present in
                  the request. The Host header cannot be used with this attribute.
                  Deprecated: use Ingress'' "konghq.com/headers" annotation instead.'
                type: object
              https_redirect_status_code:
                description: 'HTTPSRedirectStatusCode is the status code Kong responds
                  with when all properties of a Route match except the protocol. Deprecated:
                  use Ingress'' "ingress.kubernetes.io/force-ssl-redirect" or "konghq.com/https-redirect-status-code"
                  annotations instead.'
                type: integer
              methods:
                description: 'Methods is a list of HTTP methods that match this Route.
                  Deprecated: use Ingress'' "konghq.com/methods" annotation instead.'
                items:
                  type: string
                type: array
              path_handling:
                description: 'PathHandling controls how the Service path, Route path
                  and requested path are combined when sending a request to the upstream.
                  Deprecated: use Ingress'' "konghq.com/path-handling" annotation
                  instead.'
                enum:
                - v0
                - v1
                type: string
              preserve_host:
                description: 'PreserveHost sets When matching a Route via one of the
                  hosts domain names, use the request Host header in the upstream
                  request headers. If set to false, the upstream Host header will
                  be that of the Service’s host. Deprecated: use Ingress'' "konghq.com/preserve-host"
                  annotation instead.'
                type: boolean
              protocols:
                description: 'Protocols is an array of the protocols this Route should
                  allow. Deprecated: use Ingress'' "konghq.com/protocols" annotation
                  instead.'
                items:
                  description: KongProtocol is a valid Kong protocol. This alias is
                    necessary to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342
                  enum:
                  - http
                  - https
                  - grpc
                  - grpcs
                  - tcp
                  - tls
                  - udp
                  type: string
                type: array
              regex_priority:
                description: 'RegexPriority is a number used to choose which route
                  resolves a given request when several routes match it using regexes
                  simultaneously. Deprecated: use Ingress'' "konghq.com/regex-priority"
                  annotation instead.'
                type: integer
              request_buffering:
                description: 'RequestBuffering sets whether to enable request body
                  buffering or not. Deprecated: use Ingress'' "konghq.com/request-buffering"
                  annotation instead.'
                type: boolean
              response_buffering:
                description: 'ResponseBuffering sets whether to enable response body
                  buffering or not. Deprecated: use Ingress'' "konghq.com/response-buffering"
                  annotation instead.'
                type: boolean
              snis:
                description: 'SNIs is a list of SNIs that match this Route when using
                  stream routing. Deprecated: use Ingress'' "konghq.com/snis" annotation
                  instead.'
                items:
                  type: string
                type: array
              strip_path:
                description: 'StripPath sets When matching a Route via one of the
                  paths strip the matching prefix from the upstream request URL. Deprecated:
                  use Ingress'' "konghq.com/strip-path" annotation instead.'
                type: boolean
            type: object
          upstream:
            description: Upstream represents a virtual hostname and can be used to
              loadbalance incoming requests over multiple targets (e.g. Kubernetes
              `Services` can be a target, OR `Endpoints` can be targets).
            properties:
              algorithm:
                description: 'Algorithm is the load balancing algorithm to use. Accepted
                  values are: "round-robin", "consistent-hashing", "least-connections",
                  "latency".'
                enum:
                - round-robin
                - consistent-hashing
                - least-connections
                - latency
                type: string
              hash_fallback:
                description: 'HashFallback defines What to use as hashing input if
                  the primary hash_on does not return a hash. Accepted values are:
                  "none", "consumer", "ip", "header", "cookie".'
                type: string
              hash_fallback_header:
                description: HashFallbackHeader is the header name to take the value
                  from as hash input. Only required when "hash_fallback" is set to
                  "header".
                type: string
              hash_fallback_query_arg:
                description: HashFallbackQueryArg is the "hash_fallback" version of
                  HashOnQueryArg.
                type: string
              hash_fallback_uri_capture:
                description: HashFallbackURICapture is the "hash_fallback" version
                  of HashOnURICapture.
                type: string
              hash_on:
                description: 'HashOn defines what to use as hashing input. Accepted
                  values are: "none", "consumer", "ip", "header", "cookie", "path",
                  "query_arg", "uri_capture".'
                type: string
              hash_on_cookie:
                description: The cookie name to take the value from as hash input.
                  Only required when "hash_on" or "hash_fallback" is set to "cookie".
                type: string
              hash_on_cookie_path:
                description: The cookie path to set in the response headers. Only
                  required when "hash_on" or "hash_fallback" is set to "cookie".
                type: string
              hash_on_header:
                description: HashOnHeader defines the header name to take the value
                  from as hash input. Only required when "hash_on" is set to "header".
                type: string
              hash_on_query_arg:
                description: HashOnQueryArg is the query string parameter whose value
                  is the hash input when "hash_on" is set to "query_arg".
                type: string
              hash_on_uri_capture:
                description: HashOnURICapture is the name of the capture group whose
                  value is the hash input when "hash_on" is set to "uri_capture".
                type: string
              healthchecks:
                description: Healthchecks defines the health check configurations
                  in Kong.
                properties:
                  active:
                    description: ActiveHealthcheck configures active health check
                      probing.
                    properties:
                      concurrency:
                        minimum: 1
                        type: integer
                      headers:
                        additionalProperties:
                          items:
                            type: string
                          type: array
                        type: object
                      healthy:
                        description: Healthy configures thresholds and HTTP status
                          codes to mark targets healthy for an upstream.
                        properties:
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          successes:
                            minimum: 0
                            type: integer
                        type: object
                      http_path:
                        pattern: ^/.*$
                        type: string
                      https_sni:
                        type: string
                      https_verify_certificate:
                        type: boolean
                      timeout:
                        minimum: 0
                        type: integer
                      type:
                        type: string
                      unhealthy:
                        description: Unhealthy configures thresholds and HTTP status
                          codes to mark targets unhealthy.
                        properties:
                          http_failures:
                            minimum: 0
                            type: integer
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          tcp_failures:
                            minimum: 0
                            type: integer
                          timeouts:
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  passive:
                    description: PassiveHealthcheck configures passive checks around
                      passive health checks.
                    properties:
                      healthy:
                        description: Healthy configures thresholds and HTTP status
                          codes to mark targets healthy for an upstream.
                        properties:
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          successes:
                            minimum: 0
                            type: integer
                        type: object
                      type:
                        type: string
                      unhealthy:
                        description: Unhealthy configures thresholds and HTTP status
                          codes to mark targets unhealthy.
                        properties:
                          http_failures:
                            minimum: 0
                            type: integer
                          http_statuses:
                            items:
                              type: integer
                            type: array
                          interval:
                            minimum: 0
                            type: integer
                          tcp_failures:
                            minimum: 0
                            type: integer
                          timeouts:
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  threshold:
                    type: number
                type: object
              host_header:
                description: HostHeader is The hostname to be used as Host header
                  when proxying requests through Kong.
                type: string
              slots:
                description: Slots is the number of slots in the load balancer algorithm.
                minimum: 10
                type: integer
            type: object
        type: object
        x-kubernetes-validations:
        - message: '''proxy'' field is no longer supported, use Service''s annotations
            instead'
          rule: '!has(self.proxy)'
        - message: '''route'' field is no longer supported, use Ingress'' annotations
            instead'
          rule: '!has(self.route)'
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: kongplugins.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongPlugin
    listKind: KongPluginList
    plural: kongplugins
    shortNames:
    - kp
    singular: kongplugin
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Name of the plugin
      jsonPath: .plugin
      name: Plugin-Type
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Indicates if the plugin is disabled
      jsonPath: .disabled
      name: Disabled
      priority: 1
      type: boolean
    - description: Configuration of the plugin
      jsonPath: .config
      name: Config
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: KongPlugin is the Schema for the kongplugins API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          config:
            description: Config contains the plugin configuration. It's a list of
              keys and values required to configure the plugin. Please read the documentation
              of the plugin being configured to set values in here. For any plugin
              in Kong, anything that goes in the `config` JSON key in the Admin API
              request, goes into this property. Only one of `config` or `configFrom`
              may be used in a KongPlugin, not both at once.
            type: object
            x-kubernetes-preserve-unknown-fields: true
          configFrom:
            description: ConfigFrom references a secret containing the plugin configuration.
              This should be used when the plugin configuration contains sensitive
              information, such as AWS credentials in the Lambda plugin or the client
              secret in the OIDC plugin. Only one of `config` or `configFrom` may
              be used in a KongPlugin, not both at once.
            properties:
              secretKeyRef:
                description: Specifies a name and a key of a secret to refer to. The
                  namespace is implicitly set to the one of referring object.
                properties:
                  key:
                    description: The key containing the value.
                    type: string
                  name:
                    description: The secret containing the key.
                    type: string
                required:
                - key
                - name
                type: object
            type: object
          consumerRef:
            description: ConsumerRef is a reference to a particular consumer.
            type: string
          disabled:
            description: Disabled set if the plugin is disabled or not.
            type: boolean
          instance_name:
            description: InstanceName is an optional custom name to identify an instance
              of the plugin. This is useful when running the same plugin in multiple
              contexts, for example, on multiple services.
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          ordering:
            description: 'Ordering overrides the normal plugin execution order. It''s
              only available on Kong Enterprise. `<phase>` is a request processing
              phase (for example, `access` or `body_filter`) and `<plugin>` is the
              name of the plugin that will run before or after the KongPlugin. For
              example, a KongPlugin with `plugin: rate-limiting` and `before.access:
              ["key-auth"]` will create a rate limiting plugin that limits requests
              _before_ they are authenticated.'
            properties:
              after:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
              before:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
            type: object
          plugin:
            description: PluginName is the name of the plugin to which to apply the
              config.
            type: string
          protocols:
            description: Protocols configures plugin to run on requests received on
              specific protocols.
            items:
              description: KongProtocol is a valid Kong protocol. This alias is necessary
                to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342
              enum:
              - http
              - https
              - grpc
              - grpcs
              - tcp
              - tls
              - udp
              type: string
            type: array
          run_on:
            description: RunOn configures the plugin to run on the first or the second
              or both nodes in case of a service mesh deployment.
            enum:
            - first
            - second
            - all
            type: string
          status:
            description: Status represents the current status of the KongPlugin resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: "Conditions describe the current conditions of the KongPluginStatus.
                  \n Known condition types are: \n * \"Programmed\""
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - plugin
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  labels:
    gateway.networking.k8s.io/policy: direct
  name: kongupstreampolicies.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongUpstreamPolicy
    listKind: KongUpstreamPolicyList
    plural: kongupstreampolicies
    shortNames:
    - kup
    singular: kongupstreampolicy
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: "KongUpstreamPolicy allows configuring algorithm that should
          be used for load balancing traffic between Kong Upstream's Targets. It also
          allows configuring health checks for Kong Upstream's Targets. \n Its configuration
          is similar to Kong Upstream object (https://docs.konghq.com/gateway/latest/admin-api/#upstream-object),
          and it is applied to Kong Upstream objects created by the controller. \n
          It can be attached to Services. To attach it to a Service, it has to be
          annotated with `konghq.com/upstream-policy: <name>`, where `<name>` is the
          name of the KongUpstreamPolicy object in the same namespace as the Service.
          \n When attached to a Service, it will affect all Kong Upstreams created
          for the Service. \n When attached to a Service used in a Gateway API *Route
          rule with multiple BackendRefs, all of its Services MUST be configured with
          the same KongUpstreamPolicy. Otherwise, the controller will *ignore* the
          KongUpstreamPolicy. \n Note: KongUpstreamPolicy doesn't implement Gateway
          API's GEP-713 strictly. In particular, it doesn't use the TargetRef for
          attaching to Services and Gateway API *Routes - annotations are used instead.
          This is to allow reusing the same KongUpstreamPolicy for multiple Services
          and Gateway API *Routes."
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Spec contains the configuration of the Kong upstream.
            properties:
              algorithm:
                description: 'Algorithm is the load balancing algorithm to use. Accepted
                  values are: "round-robin", "consistent-hashing", "least-connections",
                  "latency".'
                enum:
                - round-robin
                - consistent-hashing
                - least-connections
                - latency
                type: string
              hashOn:
                description: HashOn defines how to calculate hash for consistent-hashing
                  load balancing algorithm. Algorithm must be set to "consistent-hashing"
                  for this field to have effect.
                properties:
                  cookie:
                    description: Cookie is the name of the cookie to use as hash input.
                    type: string
                  cookiePath:
                    description: CookiePath is cookie path to set in the response
                      headers.
                    type: string
                  header:
                    description: Header is the name of the header to use as hash input.
                    type: string
                  input:
                    description: Input allows using one of the predefined inputs (ip,
                      consumer, path). For other parametrized inputs, use one of the
                      fields below.
                    enum:
                    - ip
                    - consumer
                    - path
                    type: string
                  queryArg:
                    description: QueryArg is the name of the query argument to use
                      as hash input.
                    type: string
                  uriCapture:
                    description: URICapture is the name of the URI capture group to
                      use as hash input.
                    type: string
                type: object
              hashOnFallback:
                description: HashOnFallback defines how to calculate hash for consistent-hashing
                  load balancing algorithm if the primary hash function fails. Algorithm
                  must be set to "consistent-hashing" for this field to have effect.
                properties:
                  cookie:
                    description: Cookie is the name of the cookie to use as hash input.
                    type: string
                  cookiePath:
                    description: CookiePath is cookie path to set in the response
                      headers.
                    type: string
                  header:
                    description: Header is the name of the header to use as hash input.
                    type: string
                  input:
                    description: Input allows using one of the predefined inputs (ip,
                      consumer, path). For other parametrized inputs, use one of the
                      fields below.
                    enum:
                    - ip
                    - consumer
                    - path
                    type: string
                  queryArg:
                    description: QueryArg is the name of the query argument to use
                      as hash input.
                    type: string
                  uriCapture:
                    description: URICapture is the name of the URI capture group to
                      use as hash input.
                    type: string
                type: object
              healthchecks:
                description: Healthchecks defines the health check configurations
                  in Kong.
                properties:
                  active:
                    description: Active configures active health check probing.
                    properties:
                      concurrency:
                        description: Concurrency is the number of targets to check
                          concurrently.
                        minimum: 1
                        type: integer
                      headers:
                        additionalProperties:
                          items:
                            type: string
                          type: array
                        description: Headers is a list of HTTP headers to add to the
                          probe request.
                        type: object
                      healthy:
                        description: Healthy configures thresholds and HTTP status
                          codes to mark targets healthy for an upstream.
                        properties:
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a success.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in a healthy
                              state.
                            minimum: 0
                            type: integer
                          successes:
                            description: Successes is the number of successes to consider
                              a target healthy.
                            minimum: 0
                            type: integer
                        type: object
                      httpPath:
                        description: HTTPPath is the path to use in GET HTTP request
                          to run as a probe.
                        pattern: ^/.*$
                        type: string
                      httpsSni:
                        description: HTTPSSNI is the SNI to use in GET HTTPS request
                          to run as a probe.
                        type: string
                      httpsVerifyCertificate:
                        description: HTTPSVerifyCertificate is a boolean value that
                          indicates if the certificate should be verified.
                        type: boolean
                      timeout:
                        description: Timeout is the probe timeout in seconds.
                        minimum: 0
                        type: integer
                      type:
                        description: Type determines whether to perform active health
                          checks using HTTP or HTTPS, or just attempt a TCP connection.
                          Accepted values are "http", "https", "tcp", "grpc", "grpcs".
                        enum:
                        - http
                        - https
                        - tcp
                        - grpc
                        - grpcs
                        type: string
                      unhealthy:
                        description: Unhealthy configures thresholds and HTTP status
                          codes to mark targets unhealthy for an upstream.
                        properties:
                          httpFailures:
                            description: HTTPFailures is the number of failures to
                              consider a target unhealthy.
                            minimum: 0
                            type: integer
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a failure.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in an unhealthy
                              state.
                            minimum: 0
                            type: integer
                          tcpFailures:
                            description: TCPFailures is the number of TCP failures
                              in a row to consider a target unhealthy.
                            minimum: 0
                            type: integer
                          timeouts:
                            description: Timeouts is the number of timeouts in a row
                              to consider a target unhealthy.
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  passive:
                    description: Passive configures passive health check probing.
                    properties:
                      healthy:
                        description: Healthy configures thresholds and HTTP status
                          codes to mark targets healthy for an upstream.
                        properties:
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a success.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in a healthy
                              state.
                            minimum: 0
                            type: integer
                          successes:
                            description: Successes is the number of successes to consider
                              a target healthy.
                            minimum: 0
                            type: integer
                        type: object
                      type:
                        description: Type determines whether to perform passive health
                          checks interpreting HTTP/HTTPS statuses, or just check for
                          TCP connection success. Accepted values are "http", "https",
                          "tcp", "grpc", "grpcs".
                        enum:
                        - http
                        - https
                        - tcp
                        - grpc
                        - grpcs
                        type: string
                      unhealthy:
                        description: Unhealthy configures thresholds and HTTP status
                          codes to mark targets unhealthy.
                        properties:
                          httpFailures:
                            description: HTTPFailures is the number of failures to
                              consider a target unhealthy.
                            minimum: 0
                            type: integer
                          httpStatuses:
                            description: HTTPStatuses is a list of HTTP status codes
                              that Kong considers a failure.
                            items:
                              description: HTTPStatus is an HTTP status code.
                              maximum: 599
                              minimum: 100
                              type: integer
                            type: array
                          interval:
                            description: Interval is the interval between active health
                              checks for an upstream in seconds when in an unhealthy
                              state.
                            minimum: 0
                            type: integer
                          tcpFailures:
                            description: TCPFailures is the number of TCP failures
                              in a row to consider a target unhealthy.
                            minimum: 0
                            type: integer
                          timeouts:
                            description: Timeouts is the number of timeouts in a row
                              to consider a target unhealthy.
                            minimum: 0
                            type: integer
                        type: object
                    type: object
                  threshold:
                    description: Threshold is the minimum percentage of the upstream’s
                      targets’ weight that must be available for the whole upstream
                      to be considered healthy.
                    type: integer
                type: object
              slots:
                description: Slots is the number of slots in the load balancer algorithm.
                  If not set, the default value in Kong for the algorithm is used.
                maximum: 65536
                minimum: 10
                type: integer
            type: object
        type: object
        x-kubernetes-validations:
        - message: Only one of spec.hashOn.(input|cookie|header|uriCapture|queryArg)
            can be set.
          rule: 'has(self.spec.hashOn) ? [has(self.spec.hashOn.input), has(self.spec.hashOn.cookie),
            has(self.spec.hashOn.header), has(self.spec.hashOn.uriCapture), has(self.spec.hashOn.queryArg)].filter(fieldSet,
            fieldSet == true).size() <= 1 : true'
        - message: When spec.hashOn.cookie is set, spec.hashOn.cookiePath is required.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookie) ? has(self.spec.hashOn.cookiePath)
            : true'
        - message: When spec.hashOn.cookiePath is set, spec.hashOn.cookie is required.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookiePath) ? has(self.spec.hashOn.cookie)
            : true'
        - message: spec.algorithm must be set to "consistent-hashing" when spec.hashOn
            is set.
          rule: 'has(self.spec.hashOn) ? has(self.spec.algorithm) && self.spec.algorithm
            == "consistent-hashing" : true'
        - message: Only one of spec.hashOnFallback.(input|header|uriCapture|queryArg)
            can be set.
          rule: 'has(self.spec.hashOnFallback) ? [has(self.spec.hashOnFallback.input),
            has(self.spec.hashOnFallback.header), has(self.spec.hashOnFallback.uriCapture),
            has(self.spec.hashOnFallback.queryArg)].filter(fieldSet, fieldSet == true).size()
            <= 1 : true'
        - message: spec.algorithm must be set to "consistent-hashing" when spec.hashOnFallback
            is set.
          rule: 'has(self.spec.hashOnFallback) ? has(self.spec.algorithm) && self.spec.algorithm
            == "consistent-hashing" : true'
        - message: spec.hashOnFallback.cookie must not be set.
          rule: 'has(self.spec.hashOnFallback) ? !has(self.spec.hashOnFallback.cookie)
            : true'
        - message: spec.hashOnFallback.cookiePath must not be set.
          rule: 'has(self.spec.hashOnFallback) ? !has(self.spec.hashOnFallback.cookiePath)
            : true'
        - message: spec.healthchecks.passive.healthy.interval must not be set.
          rule: 'has(self.spec.healthchecks) && has(self.spec.healthchecks.passive)
            && has(self.spec.healthchecks.passive.healthy) ? !has(self.spec.healthchecks.passive.healthy.interval)
            : true'
        - message: spec.healthchecks.passive.unhealthy.interval must not be set.
          rule: 'has(self.spec.healthchecks) && has(self.spec.healthchecks.passive)
            && has(self.spec.healthchecks.passive.unhealthy) ? !has(self.spec.healthchecks.passive.unhealthy.interval)
            : true'
        - message: spec.hashOnFallback must not be set when spec.hashOn.cookie is
            set.
          rule: 'has(self.spec.hashOn) && has(self.spec.hashOn.cookie) ? !has(self.spec.hashOnFallback)
            : true'
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: tcpingresses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: TCPIngress
    listKind: TCPIngressList
    plural: tcpingresses
    singular: tcpingress
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Address of the load balancer
      jsonPath: .status.loadBalancer.ingress[*].ip
      name: Address
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: TCPIngress is the Schema for the tcpingresses API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the TCPIngress specification.
            properties:
              rules:
                description: A list of rules used to configure the Ingress.
                items:
                  description: IngressRule represents a rule to apply against incoming
                    requests. Matching is performed based on an (optional) SNI and
                    port.
                  properties:
                    backend:
                      description: Backend defines the referenced service endpoint
                        to which the traffic will be forwarded to.
                      properties:
                        serviceName:
                          description: Specifies the name of the referenced service.
                          minLength: 1
                          type: string
                        servicePort:
                          description: Specifies the port of the referenced service.
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - serviceName
                      - servicePort
                      type: object
                    host:
                      description: Host is the fully qualified domain name of a network
                        host, as defined by RFC 3986. If a Host is not specified,
                        then port-based TCP routing is performed. Kong doesn't care
                        about the content of the TCP stream in this case. If a Host
                        is specified, the protocol must be TLS over TCP. A plain-text
                        TCP request cannot be routed based on Host. It can only be
                        routed based on Port.
                      type: string
                    port:
                      description: Port is the port on which to accept TCP or TLS
                        over TCP sessions and route. It is a required field. If a
                        Host is not specified, the requested are routed based only
                        on Port.
                      format: int32
                      maximum: 65535
                      minimum: 1
                      type: integer
                  required:
                  - backend
                  - port
                  type: object
                type: array
              tls:
                description: TLS configuration. This is similar to the `tls` section
                  in the Ingress resource in networking.v1beta1 group. The mapping
                  of SNIs to TLS cert-key pair defined here will be used for HTTP
                  Ingress rules as well. Once can define the mapping in this resource
                  or the original Ingress resource, both have the same effect.
                items:
                  description: IngressTLS describes the transport layer security.
                  properties:
                    hosts:
                      description: Hosts are a list of hosts included in the TLS certificate.
                        The values in this list must match the name/s used in the
                        tlsSecret. Defaults to the wildcard host setting for the loadbalancer
                        controller fulfilling this Ingress, if left unspecified.
                      items:
                        type: string
                      type: array
                    secretName:
                      description: SecretName is the name of the secret used to terminate
                        SSL traffic.
                      type: string
                  type: object
                type: array
            type: object
          status:
            description: TCPIngressStatus defines the observed state of TCPIngress.
            properties:
              loadBalancer:
                description: LoadBalancer contains the current status of the load-balancer.
                properties:
                  ingress:
                    description: Ingress is a list containing ingress points for the
                      load-balancer. Traffic intended for the service should be sent
                      to these ingress points.
                    items:
                      description: 'LoadBalancerIngress represents the status of a
                        load-balancer ingress point: traffic intended for the service
                        should be sent to an ingress point.'
                      properties:
                        hostname:
                          description: Hostname is set for load-balancer ingress points
                            that are DNS based (typically AWS load-balancers)
                          type: string
                        ip:
                          description: IP is set for load-balancer ingress points
                            that are IP based (typically GCE or OpenStack load-balancers)
                          type: string
                        ports:
                          description: Ports is a list of records of service ports
                            If used, every port defined in the service should have
                            an entry in it
                          items:
                            properties:
                              error:
                                description: 'Error is to record the problem with
                                  the service port The format of the error shall comply
                                  with the following rules: - built-in error values
                                  shall be specified in this file and those shall
                                  use CamelCase names - cloud provider specific error
                                  values must have names that comply with the format
                                  foo.example.com/CamelCase. --- The regex it matches
                                  is (dns1123SubdomainFmt/)?(qualifiedNameFmt)'
                                maxLength: 316
                                pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                                type: string
                              port:
                                description: Port is the port number of the service
                                  port of which status is recorded here
                                format: int32
                                type: integer
                              protocol:
                                default: TCP
                                description: 'Protocol is the protocol of the service
                                  port of which status is recorded here The supported
                                  values are: "TCP", "UDP", "SCTP"'
                                type: string
                            required:
                            - port
                            - protocol
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    type: array
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: udpingresses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: UDPIngress
    listKind: UDPIngressList
    plural: udpingresses
    singular: udpingress
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Address of the load balancer
      jsonPath: .status.loadBalancer.ingress[*].ip
      name: Address
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: UDPIngress is the Schema for the udpingresses API.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the UDPIngress specification.
            properties:
              rules:
                description: A list of rules used to configure the Ingress.
                items:
                  description: UDPIngressRule represents a rule to apply against incoming
                    requests wherein no Host matching is available for request routing,
                    only the port is used to match requests.
                  properties:
                    backend:
                      description: Backend defines the Kubernetes service which accepts
                        traffic from the listening Port defined above.
                      properties:
                        serviceName:
                          description: Specifies the name of the referenced service.
                          minLength: 1
                          type: string
                        servicePort:
                          description: Specifies the port of the referenced service.
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                      required:
                      - serviceName
                      - servicePort
                      type: object
                    port:
                      description: Port indicates the port for the Kong proxy to accept
                        incoming traffic on, which will then be routed to the service
                        Backend.
                      format: int32
                      maximum: 65535
                      minimum: 1
                      type: integer
                  required:
                  - backend
                  - port
                  type: object
                type: array
            type: object
          status:
            description: UDPIngressStatus defines the observed state of UDPIngress.
            properties:
              loadBalancer:
                description: LoadBalancer contains the current status of the load-balancer.
                properties:
                  ingress:
                    description: Ingress is a list containing ingress points for the
                      load-balancer. Traffic intended for the service should be sent
                      to these ingress points.
                    items:
                      description: 'LoadBalancerIngress represents the status of a
                        load-balancer ingress point: traffic intended for the service
                        should be sent to an ingress point.'
                      properties:
                        hostname:
                          description: Hostname is set for load-balancer ingress points
                            that are DNS based (typically AWS load-balancers)
                          type: string
                        ip:
                          description: IP is set for load-balancer ingress points
                            that are IP based (typically GCE or OpenStack load-balancers)
                          type: string
                        ports:
                          description: Ports is a list of records of service ports
                            If used, every port defined in the service should have
                            an entry in it
                          items:
                            properties:
                              error:
                                description: 'Error is to record the problem with
                                  the service port The format of the error shall comply
                                  with the following rules: - built-in error values
                                  shall be specified in this file and those shall
                                  use CamelCase names - cloud provider specific error
                                  values must have names that comply with the format
                                  foo.example.com/CamelCase. --- The regex it matches
                                  is (dns1123SubdomainFmt/)?(qualifiedNameFmt)'
                                maxLength: 316
                                pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                                type: string
                              port:
                                description: Port is the port number of the service
                                  port of which status is recorded here
                                format: int32
                                type: integer
                              protocol:
                                default: TCP
                                description: 'Protocol is the protocol of the service
                                  port of which status is recorded here The supported
                                  values are: "TCP", "UDP", "SCTP"'
                                type: string
                            required:
                            - port
                            - protocol
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    type: array
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kong-leader-election
  namespace: kong
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kong-ingress
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - ingressclassparameterses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongclusterplugins
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongclusterplugins/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongconsumergroups
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongconsumergroups/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongconsumers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongconsumers/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongingresses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongingresses/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongplugins
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongplugins/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongupstreampolicies
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - kongupstreampolicies/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - tcpingresses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - tcpingresses/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - configuration.konghq.com
  resources:
  - udpingresses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - configuration.konghq.com
  resources:
  - udpingresses/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - discovery.k8s.io
  resources:
  - endpointslices
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingressclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kong-ingress-crds
rules:
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kong-ingress-gateway
rules:
- apiGroups:
  - ""
  resources:
  - namespaces
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gatewayclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gatewayclasses/status
  verbs:
  - get
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gateways
  verbs:
  - get
  - list
  - update
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gateways/status
  verbs:
  - get
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - grpcroutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - grpcroutes/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - httproutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - httproutes/status
  verbs:
  - get
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - referencegrants
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - referencegrants/status
  verbs:
  - get
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - tcproutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - tcproutes/status
  verbs:
  - get
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - tlsroutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - tlsroutes/status
  verbs:
  - get
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - udproutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - udproutes/status
  verbs:
  - get
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kong-leader-election
  namespace: kong
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kong-leader-election
subjects:
- kind: ServiceAccount
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kong-ingress
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kong-ingress
subjects:
- kind: ServiceAccount
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kong-ingress-crds
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kong-ingress-crds
subjects:
- kind: ServiceAccount
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kong-ingress-gateway
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kong-ingress-gateway
subjects:
- kind: ServiceAccount
  name: kong-serviceaccount
  namespace: kong
---
apiVersion: v1
kind: Service
metadata:
  name: kong-admin
  namespace: kong
spec:
  clusterIP: None
  ports:
  - name: admin-tls
    port: 8444
    protocol: TCP
    targetPort: 8444
  selector:
    app: proxy-kong
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
  name: kong-proxy
  namespace: kong
spec:
  ports:
  - name: proxy
    port: 80
    protocol: TCP
    targetPort: 8000
  - name: proxy-ssl
    port: 443
    protocol: TCP
    targetPort: 8443
  selector:
    app: proxy-kong
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: kong-validation-webhook
  namespace: kong
spec:
  ports:
  - name: webhook
    port: 443
    protocol: TCP
    targetPort: 8080
  selector:
    app: ingress-kong
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ingress-kong
  name: ingress-kong
  namespace: kong
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ingress-kong
  template:
    metadata:
      annotations:
        kuma.io/gateway: enabled
        kuma.io/service-account-token-volume: kong-serviceaccount-token
        traffic.sidecar.istio.io/includeInboundPorts: ""
      labels:
        app: ingress-kong
    spec:
      automountServiceAccountToken: false
      containers:
      - env:
        - name: CONTROLLER_KONNECT_SYNC_ENABLED
          value: "true"
        - name: CONTROLLER_KONNECT_TLS_CLIENT_CERT
          valueFrom:
            secretKeyRef:
              key: tls.crt
              name: konnect-client-tls
        - name: CONTROLLER_KONNECT_TLS_CLIENT_KEY
          valueFrom:
            secretKeyRef:
              key: tls.key
              name: konnect-client-tls
        - name: CONTROLLER_KONG_ADMIN_SVC
          value: kong/kong-admin
        - name: CONTROLLER_KONG_ADMIN_TLS_SKIP_VERIFY
          value: "true"
        - name: CONTROLLER_PUBLISH_SERVICE
          value: kong/kong-proxy
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        envFrom:
        - configMapRef:
            name: konnect-config
        image: kong/kubernetes-ingress-controller:3.0
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 10254
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        name: ingress-controller
        ports:
        - containerPort: 8080
          name: webhook
          protocol: TCP
        - containerPort: 10255
          name: cmetrics
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /readyz
            port: 10254
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
          name: kong-serviceaccount-token
          readOnly: true
      serviceAccountName: kong-serviceaccount
      volumes:
      - name: kong-serviceaccount-token
        projected:
          sources:
          - serviceAccountToken:
              expirationSeconds: 3607
              path: token
          - configMap:
              items:
              - key: ca.crt
                path: ca.crt
              name: kube-root-ca.crt
          - downwardAPI:
              items:
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
                path: namespace
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: proxy-kong
  name: proxy-kong
  namespace: kong
spec:
  replicas: 2
  selector:
    matchLabels:
      app: proxy-kong
  template:
    metadata:
      annotations:
        kuma.io/gateway: enabled
        kuma.io/service-account-token-volume: kong-serviceaccount-token
        traffic.sidecar.istio.io/includeInboundPorts: ""
      labels:
        app: proxy-kong
    spec:
      automountServiceAccountToken: false
      containers:
      - env:
        - name: KONG_ROUTER_FLAVOR
          value: traditional_compatible
        - name: KONG_PROXY_LISTEN
          value: 0.0.0.0:8000 reuseport backlog=16384, 0.0.0.0:8443 http2 ssl reuseport
            backlog=16384
        - name: KONG_PORT_MAPS
          value: 80:8000, 443:8443
        - name: KONG_ADMIN_LISTEN
          value: 0.0.0.0:8444 http2 ssl reuseport backlog=16384
        - name: KONG_STATUS_LISTEN
          value: 0.0.0.0:8100
        - name: KONG_DATABASE
          value: "off"
        - name: KONG_NGINX_WORKER_PROCESSES
          value: "2"
        - name: KONG_KIC
          value: "on"
        - name: KONG_ADMIN_ACCESS_LOG
          value: /dev/stdout
        - name: KONG_ADMIN_ERROR_LOG
          value: /dev/stderr
        - name: KONG_PROXY_ERROR_LOG
          value: /dev/stderr
        image: kong/kong-gateway:3.4
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/bash
              - -c
              - kong quit
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /status
            port: 8100
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        name: proxy
        ports:
        - containerPort: 8000
          name: proxy
          protocol: TCP
        - containerPort: 8443
          name: proxy-ssl
          protocol: TCP
        - containerPort: 8100
          name: metrics
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /status/ready
            port: 8100
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
      serviceAccountName: kong-serviceaccount
      volumes:
      - name: kong-serviceaccount-token
        projected:
          sources:
          - serviceAccountToken:
              expirationSeconds: 3607
              path: token
          - configMap:
              items:
              - key: ca.crt
                path: ca.crt
              name: kube-root-ca.crt
          - downwardAPI:
              items:
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
                path: namespace
---
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: kong
spec:
  controller: ingress-controllers.konghq.com/kong
