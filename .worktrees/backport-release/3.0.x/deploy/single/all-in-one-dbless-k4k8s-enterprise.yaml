# Generated by build-single-manifest.sh. DO NOT EDIT.
#
# DEPRECATED
#
# For Kong Ingress Controller 3.0+, please use <PERSON><PERSON> instead:
#
#   $ helm repo add kong https://charts.konghq.com
#   $ helm repo update
#   $ helm install kong/kong --generate-name --set ingressController.installCRDs=false
#
# If you intend to use an older version, <PERSON><PERSON> is recommended but you still have the option
# to install using manifests. In that case, replace the 'main' branch in your link with the
# KIC tag. For example:
# kubectl apply -f https://raw.githubusercontent.com/Kong/kubernetes-ingress-controller/v2.12.0/deploy/single/all-in-one-dbless-k4k8s-enterprise.yaml
#

apiVersion: please-use-helm-to-install-kong
kind: Deprecated
