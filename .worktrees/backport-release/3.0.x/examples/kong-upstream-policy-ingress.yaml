---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: httpbin-deployment
  labels:
    app: httpbin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: httpbin
  template:
    metadata:
      labels:
        app: httpbin
    spec:
      containers:
        - name: httpbin
          image: kong/httpbin:0.1.0
          ports:
            - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: httpbin
  name: httpbin-deployment
  annotations:
    konghq.com/upstream-policy: httpbin-upstream-policy
spec:
  ports:
    - name: http1
      port: 80
      protocol: TCP
      targetPort: 80
    - name: http2
      port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    app: httpbin
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: httpbin-ingress
  annotations:
    konghq.com/strip-path: "true"
spec:
  ingressClassName: kong
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: httpbin-deployment
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: httpbin-ingress-2
  annotations:
    konghq.com/strip-path: "true"
spec:
  ingressClassName: kong
  rules:
    - http:
        paths:
          - path: /other-path
            pathType: Prefix
            backend:
              service:
                name: httpbin-deployment
                port:
                  number: 8080 # Note: it's a different port than the previous Ingress uses.
---
# This policy will be applied to every Upstream that is created for
# the httpbin-deployment Service. In this case those would be following Kong Upstreams:
# - default.httpbin-deployment.80
# - default.httpbin-deployment.8080
apiVersion: configuration.konghq.com/v1beta1
kind: KongUpstreamPolicy
metadata:
  name: httpbin-upstream-policy
spec:
  algorithm: consistent-hashing
  slots: 100
  hashOn:
    cookie: session-id
    cookiePath: cookie-path
  healthchecks:
    active:
      type: tcp
      concurrency: 20
      healthy:
        interval: 5
        successes: 5
      unhealthy:
        timeouts: 5
        interval: 10
    passive:
      type: http
      healthy:
        successes: 5
      unhealthy:
        timeouts: 10
