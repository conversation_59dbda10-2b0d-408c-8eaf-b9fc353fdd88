# NOTE The Gateway APIs are not yet available by default in Kubernetes.
# Follow these instructions to install them before using this example:
# https://gateway-api.sigs.k8s.io/guides/#install-experimental-channel
---
apiVersion: v1
kind: Service
metadata:
  name: grpcbin
  labels:
    app: grpcbin
spec:
  ports:
  - name: grpc
    port: 443
    targetPort: 9001
  selector:
    app: grpcbin
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grpcbin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grpcbin
  template:
    metadata:
      labels:
        app: grpcbin
    spec:
      containers:
      - image: kong/grpcbin
        name: grpcbin
        ports:
        - containerPort: 9001
---
apiVersion: v1
kind: Secret
metadata:
  name: grpcroute-example
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQW9TZ0F3SUJBZ0lVVkw2VVlWRGRINnBlVk5TT25Pa0N1WXlobXJzd0NnWUlLb1pJemowRUF3SXcKZ2JReEN6QUpCZ05WQkFZVEFsVlRNUk13RVFZRFZRUUlEQXBEWVd4cFptOXlibWxoTVJZd0ZBWURWUVFIREExVApZVzRnUm5KaGJtTnBjMk52TVJNd0VRWURWUVFLREFwTGIyNW5MQ0JKYm1NdU1SZ3dGZ1lEVlFRTERBOVVaV0Z0CklFdDFZbVZ5Ym1WMFpYTXhIakFjQmdOVkJBTU1GWFJzYzNKdmRYUmxMbXR2Ym1jdVpYaGhiWEJzWlRFcE1DY0cKQ1NxR1NJYjNEUUVKQVJZYWRHVnpkRUIwYkhOeWIzVjBaUzVyYjI1bkxtVjRZVzF3YkdVd0lCY05Nakl3TmpFMgpNakV4TWpJNFdoZ1BNakV5TWpBMU1qTXlNVEV5TWpoYU1JRzBNUXN3Q1FZRFZRUUdFd0pWVXpFVE1CRUdBMVVFCkNBd0tRMkZzYVdadmNtNXBZVEVXTUJRR0ExVUVCd3dOVTJGdUlFWnlZVzVqYVhOamJ6RVRNQkVHQTFVRUNnd0sKUzI5dVp5d2dTVzVqTGpFWU1CWUdBMVVFQ3d3UFZHVmhiU0JMZFdKbGNtNWxkR1Z6TVI0d0hBWURWUVFEREJWMApiSE55YjNWMFpTNXJiMjVuTG1WNFlXMXdiR1V4S1RBbkJna3Foa2lHOXcwQkNRRVdHblJsYzNSQWRHeHpjbTkxCmRHVXVhMjl1Wnk1bGVHRnRjR3hsTUhZd0VBWUhLb1pJemowQ0FRWUZLNEVFQUNJRFlnQUVRZWNmenN4bVB3QzAKNnVOczNreWlMRGI2YnJuZ000WnRHWGd3Y0dEMzkzY2JZbWF1bmZCUFJ0eHFoNzZSS2RTOXd6cTRxK29COGRQcwpRS2dCTmhsSlRyK2lGSDlEaTdiQlpGY1lxeCtTbk5VWFowZEROQmJXNHJQVlRKSFF2ZG9ubzFNd1VUQWRCZ05WCkhRNEVGZ1FVK09PVmJxTWN1K3lYb21aZm5aNTRMZ0lSTm80d0h3WURWUjBqQkJnd0ZvQVUrT09WYnFNY3UreVgKb21aZm5aNTRMZ0lSTm80d0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBS0JnZ3Foa2pPUFFRREFnTm9BREJsQWpCdQpQTXErVCtpVEoweU52bGRZcEIzQmZkSWhydjBFSlE5QUxiQjE2bkp3RjkxWVY2WUU3bWROUDVyTlZub1owbkFDCk1RRG1uSXBpcE1hd2pKV3BmU1BTWlMxL2lBcno4WXVCcm9XckdGWFA2Mmx3aENVcDhSWndlTm5yTG1tYi9BZWsKeTNvPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  tls.key: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
type: kubernetes.io/tls
---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: kong
  annotations:
    konghq.com/gatewayclass-unmanaged: "true"
spec:
  controllerName: konghq.com/kic-gateway-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: kong
spec:
  gatewayClassName: kong
  listeners:
  - name: grpc
    protocol: HTTPS
    port: 443
    tls:
      certificateRefs:
      - name: grpcroute-example
---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: GRPCRoute
metadata:
  name: grpcbin
spec:
  parentRefs:
  - name: kong
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: grpcbin
      port: 443
    matches:
    - method:
        service: "grpcbin.GRPCBin"
        method: "DummyUnary"
