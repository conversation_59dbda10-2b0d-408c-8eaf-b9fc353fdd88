# NOTE The Gateway APIs are not yet available by default in Kubernetes.
# Follow these instructions to install them before using this example:
# https://gateway-api.sigs.k8s.io/guides/#install-experimental-channel
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tlsecho
  labels:
    app: tlsecho
spec:
  selector:
    matchLabels:
      app: tlsecho
  template:
    metadata:
      labels:
        app: tlsecho
    spec:
      containers:
      - name: tlsecho
        image: kong/go-echo:0.3.0
        ports:
        - containerPort: 1030
        env:
        - name: POD_NAME
          value: tlsroute-example-manifest
        - name: TLS_PORT
          value: "1030"
        - name: TLS_CERT_FILE
          value: /var/run/certs/tls.crt
        - name: TLS_KEY_FILE
          value: /var/run/certs/tls.key
        volumeMounts:
        - mountPath: /var/run/certs
          name: secret-test
          readOnly: true
      volumes:
      - name: secret-test
        secret:
          defaultMode: 420
          secretName: tlsroute-example
---
apiVersion: v1
kind: Service
metadata:
  name: tlsecho
spec:
  ports:
  - port: 8899
    protocol: TCP
    targetPort: 1030
  selector:
    app: tlsecho
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: tlsroute-example
data:
  tls.crt: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  tls.key: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
type: kubernetes.io/tls
---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: kong
  annotations:
    konghq.com/gatewayclass-unmanaged: "true"
spec:
  controllerName: konghq.com/kic-gateway-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: kong
spec:
  gatewayClassName: kong
  listeners:
  - name: http
    protocol: HTTP
    port: 80
  - name: tls
    protocol: TLS
    port: 8899
    hostname: tlsroute.kong.example
    tls:
      mode: Passthrough
      certificateRefs:
      - name: tlsroute-example
---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TLSRoute
metadata:
  name: tlsecho
spec:
  parentRefs:
  - name: kong
  hostnames:
  - tlsroute.kong.example
  rules:
  - backendRefs:
    - name: tlsecho
      port: 8899
