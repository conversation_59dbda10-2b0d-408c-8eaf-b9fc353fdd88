---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: GatewayClass
metadata:
  name: kong
  annotations:
    konghq.com/gatewayclass-unmanaged: "true"
spec:
  controllerName: konghq.com/kic-gateway-controller
---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: Gateway
metadata:
  name: kong
spec:
  gatewayClassName: kong
  listeners:
    - name: http
      protocol: HTTP
      port: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: httpbin-prod
  labels:
    app: httpbin-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: httpbin-prod
  template:
    metadata:
      labels:
        app: httpbin-prod
    spec:
      containers:
        - name: httpbin
          image: kong/httpbin:0.1.0
          ports:
            - containerPort: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: httpbin-test
  labels:
    app: httpbin-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: httpbin-test
  template:
    metadata:
      labels:
        app: httpbin-test
    spec:
      containers:
        - name: httpbin
          image: kong/httpbin:0.1.0
          ports:
            - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: httpbin-prod
  name: httpbin-prod
  annotations:
    konghq.com/upstream-policy: httpbin-httproute
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: httpbin-prod
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: httpbin-test
  name: httpbin-test
  annotations:
    konghq.com/upstream-policy: httpbin-httproute
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: httpbin
  type: ClusterIP
---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: httpbin
  annotations:
    konghq.com/strip-path: "true"
spec:
  parentRefs:
    - name: kong
  rules:
    - matches: # This rule will generate an Upstream with the name `httproute.default.httpbin.0`.
        - path:
            type: PathPrefix
            value: /httpbin-with-test
      backendRefs:
        - name: httpbin-prod
          kind: Service
          port: 80
          weight: 75
        - name: httpbin-test
          kind: Service
          port: 80
          weight: 25
    - matches: # This rule will generate an Upstream with the name `httproute.default.httpbin.1`.
        - path:
            type: PathPrefix
            value: /httpbin-prod-only
      backendRefs:
        - name: httpbin-prod
          kind: Service
          port: 80
---
# This policy will be applied to all Upstreams generated for the annotated Services:
# - httproute.default.httpbin.0
# - httproute.default.httpbin.1
apiVersion: configuration.konghq.com/v1beta1
kind: KongUpstreamPolicy
metadata:
  name: httpbin-httproute
spec:
  algorithm: consistent-hashing
  slots: 100
  hashOn:
    cookie: session-id
    cookiePath: cookie-path
  healthchecks:
    active:
      type: tcp
      concurrency: 20
      healthy:
        interval: 5
        successes: 5
      unhealthy:
        timeouts: 5
        interval: 10
    passive:
      type: http
      healthy:
        successes: 5
      unhealthy:
        timeouts: 10
