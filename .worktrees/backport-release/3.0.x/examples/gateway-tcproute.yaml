# NOTE The Gateway APIs are not yet available by default in Kubernetes.
# Follow these instructions to install them before using this example:
# https://gateway-api.sigs.k8s.io/guides/#install-experimental-channel
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tcpecho
  labels:
    app: tcpecho
spec:
  selector:
    matchLabels:
      app: tcpecho
  template:
    metadata:
      labels:
        app: tcpecho
    spec:
      containers:
      - name: tcpecho
        image: kong/go-echo:0.3.0
        ports:
        - containerPort: 1025
        env:
        - name: POD_NAME
          value: tcproute-example-manifest
---
apiVersion: v1
kind: Service
metadata:
  name: tcpecho
spec:
  ports:
  - port: 8888
    protocol: TCP
    targetPort: 1025
  selector:
    app: tcpecho
  type: ClusterIP
---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: kong
  annotations:
    konghq.com/gatewayclass-unmanaged: "true"
spec:
  controllerName: konghq.com/kic-gateway-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: kong
spec:
  gatewayClassName: kong
  listeners:
  - name: http
    protocol: HTTP
    port: 80
  - name: tcp
    protocol: TCP
    port: 8888
---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TCPRoute
metadata:
  name: tcpecho
spec:
  parentRefs:
  - name: kong
  rules:
  - backendRefs:
    - name: tcpecho
      port: 8888
