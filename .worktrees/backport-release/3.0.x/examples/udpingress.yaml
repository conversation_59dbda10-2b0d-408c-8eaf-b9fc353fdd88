# Usage:
#
# This example sets up a coredns deployment which can be proxied to via Kong UDPIngress.
# In order to use this example make sure you're running the controller manager with the
# following flags set:
#
#   --enable-controller-udpingress=true
#   --ingress-class=kong
#
# Before hand you will also need to configure the controller with a UDP listener and expose
# that (via a LoadBalancer service ideally for testing). The proxy container will need the
# `KONG_STREAM_LISTEN` environment variable set in the containers env:
#
#   - name: KONG_STREAM_LISTEN
#     value: 0.0.0.0:9999 udp reuseport
#
# And then create a service configured best according to your environment to expose this port
# on the proxy container or use a shortcut like `kubectl expose <options>`.
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: coredns
data:
  Corefile: |-
    .:53 {
        errors
        health {
           lameduck 5s
        }
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
           pods insecure
           fallthrough in-addr.arpa ip6.arpa
           ttl 30
        }
        forward . /etc/resolv.conf {
           max_concurrent 1000
        }
        cache 30
        loop
        reload
        loadbalance
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coredns
  labels:
    app: coredns
spec:
  replicas: 1
  selector:
    matchLabels:
      app: coredns
  template:
    metadata:
      labels:
        app: coredns
    spec:
      containers:
      - args:
        - -conf
        - /etc/coredns/Corefile
        image: registry.k8s.io/coredns/coredns:v1.8.6
        imagePullPolicy: IfNotPresent
        name: coredns
        ports:
        - containerPort: 53
          protocol: UDP
        volumeMounts:
        - mountPath: /etc/coredns
          name: config-volume
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: Corefile
            path: Corefile
          name: coredns
        name: config-volume
---
apiVersion: v1
kind: Service
metadata:
  name: coredns
spec:
  ports:
  - port: 53
    protocol: UDP
    targetPort: 53
  selector:
    app: coredns
  type: ClusterIP
---
apiVersion: configuration.konghq.com/v1beta1
kind: UDPIngress
metadata:
  name: minudp
  annotations:
    kubernetes.io/ingress.class: "kong"
spec:
  rules:
  - backend:
      serviceName: coredns
      servicePort: 53
    port: 9999
---
