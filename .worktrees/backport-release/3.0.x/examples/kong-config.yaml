apiVersion: v1
kind: Secret
metadata:
  name: kong-config
  namespace: kong-system
stringData:
  _v1_Service_default_some-service: |
    {
      "apiVersion": "v1",
      "kind": "Service",
      "metadata": {
        "namespace": "default",
        "name": "some-service"
      },
      "spec": {
        "type": "ExternalName",
        "externalName": "httpbin.com",
        "ports": [
          {
            "port": 80,
            "name": "http",
            "protocol": "TCP"
          }
        ]
      }
    }
  networking.k8s.io_v1_Ingress_default_some-ingress: |
    {
      "apiVersion": "v1",
      "kind": "Service",
      "metadata": {
        "namespace": "default",
        "name": "some-ingress"
      },
      "spec": {
        "ingressClassName": "kong",
        "rules": [
          {
            "http": {
              "paths": [
                {
                  "backend": {
                    "service": {
                      "name": "some-service",
                      "port": {
                        "number": 80
                      }
                    }
                  }
                }
              ]
            }
          }
        ]
      }
    }
