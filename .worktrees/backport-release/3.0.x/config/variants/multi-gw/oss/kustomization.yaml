apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../base

components:
  - ../../../image/oss

patches:
- target:
    group: apps
    version: v1
    kind: Deployment
    name: ingress-kong
  patch: |-
    - op: add
      path: /spec/template/metadata/annotations/traffic.kuma.io~1exclude-outbound-ports
      value: "8444"
    - op: add
      path: /spec/template/metadata/annotations/traffic.sidecar.istio.io~1excludeOutboundPorts
      value: "8444"
