---
apiVersion: v1
kind: Service
metadata:
  name: kong-admin
  namespace: kong
spec:
  externalTrafficPolicy: Local
  ports:
  - name: admin
    port: 80
    protocol: TCP
    targetPort: 8001
  selector:
    app: ingress-kong
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: kong-manager
  namespace: kong
spec:
  externalTrafficPolicy: Local
  ports:
  - name: manager
    port: 80
    protocol: TCP
    targetPort: 8002
  selector:
    app: ingress-kong
  type: LoadBalancer
