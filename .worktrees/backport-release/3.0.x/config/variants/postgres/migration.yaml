---
apiVersion: batch/v1
kind: Job
metadata:
  name: kong-migrations
  namespace: kong
spec:
  template:
    metadata:
      name: kong-migrations
    spec:
      initContainers:
      - name: wait-for-postgres
        image: kong-placeholder:placeholder
        env:
        - name: KONG_PG_HOST
          value: postgres
        - name: KONG_PG_PORT
          value: "5432"
        command: [ "/bin/bash", "-c", "until timeout 1 bash 9<>/dev/tcp/${KONG_PG_HOST}/${KONG_PG_PORT}; do echo 'waiting for db'; sleep 1; done" ]
      containers:
      - name: kong-migrations
        image: kong-placeholder:placeholder
        env:
        - name: KONG_PG_PASSWORD
          value: kong
        - name: KONG_PG_HOST
          value: postgres
        - name: KONG_PG_PORT
          value: "5432"
        command: [ "/bin/bash", "-c", "kong migrations bootstrap && kong migrations up && kong migrations finish" ]
      restartPolicy: OnFailure
