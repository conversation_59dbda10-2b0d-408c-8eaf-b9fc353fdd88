# This kustomization.yaml is not intended to be run by itself,
# since it depends on service name and namespace that are out of this kustomize package.
# It should be run by config/default
resources:
- bases/configuration.konghq.com_tcpingresses.yaml
- bases/configuration.konghq.com_udpingresses.yaml
- bases/configuration.konghq.com_kongclusterplugins.yaml
- bases/configuration.konghq.com_kongconsumers.yaml
- bases/configuration.konghq.com_kongconsumergroups.yaml
- bases/configuration.konghq.com_kongingresses.yaml
- bases/configuration.konghq.com_kongplugins.yaml
- bases/configuration.konghq.com_ingressclassparameterses.yaml
- bases/configuration.konghq.com_kongupstreampolicies.yaml
#+kubebuilder:scaffold:crdkustomizeresource

patchesStrategicMerge:
# [WEBHOOK] To enable webhook, uncomment all the sections with [WEBHOOK] prefix.
# patches here are for enabling the conversion webhook for each CRD
#- patches/webhook_in_tcpingresses.yaml
#- patches/webhook_in_udpingresses.yaml
#+kubebuilder:scaffold:crdkustomizewebhookpatch

# [CERTMANAGER] To enable webhook, uncomment all the sections with [CERTMANAGER] prefix.
# patches here are for enabling the CA injection for each CRD
#- patches/cainjection_in_tcpingresses.yaml
#- patches/cainjection_in_udpingresses.yaml
#+kubebuilder:scaffold:crdkustomizecainjectionpatch

# the following config is for teaching kustomize how to do kustomization for CRDs.
configurations:
- kustomizeconfig.yaml
