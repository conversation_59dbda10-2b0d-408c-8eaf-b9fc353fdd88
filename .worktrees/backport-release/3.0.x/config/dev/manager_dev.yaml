apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ingress-kong
  name: ingress-kong
  namespace: kong
spec:
  selector:
    matchLabels:
      app: ingress-kong
  template:
    metadata:
      labels:
        app: ingress-kong
    spec:
      containers:
      - name: ingress-controller
        args:
          - --feature-gates=GatewayAlpha=true
          - --anonymous-reports=false
        env:
        - name: CONTROLLER_LOG_LEVEL
          value: debug
        image: kic-placeholder:placeholder
