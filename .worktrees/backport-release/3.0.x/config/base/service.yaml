---
apiVersion: v1
kind: Service
metadata:
  name: kong-proxy
  namespace: kong
  annotations:
    # Cloud-provider specific annotations
    # GKE
    # GKE creates a L4 LB for any service of type LoadBalancer
    # TODO figure out how to enable Proxy Protocol on an L4 LB for GKE
    # AWS
    # Use NLB over ELB
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    # Use L4 LB so that Kong can do TLS termination
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
    # Enable Proxy Protocol when Kong is listening for proxy-protocol
    #service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: '*'
spec:
  type: LoadBalancer
  ports:
  - name: proxy
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: proxy-ssl
    port: 443
    targetPort: 8443
    protocol: TCP
  selector:
    app: ingress-kong

