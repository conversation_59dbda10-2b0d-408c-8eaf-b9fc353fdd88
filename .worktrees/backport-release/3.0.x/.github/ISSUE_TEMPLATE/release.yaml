name: Release
description: Release checklist
title: "Replace with your release version (e.g: 2.4.0)"
labels:
- area/release
body:
- type: dropdown
  id: release_type
  attributes:
    label: Release Type
    description: which type of release is this release?
    options:
    - major
    - minor
    - patch
  validations:
    required: true
- type: checkboxes
  id: release_branch
  attributes:
    label: "**For major/minor releases** Create `release/<MAJOR>.<MINOR>.x` Branch"
    options:
      # This can be automated. https://github.com/Kong/kubernetes-ingress-controller/issues/3772 tracks this effort
      - label: "Create the `release/<MAJOR>.<MINOR>.x` branch at the place where you want to branch of off main"
- type: checkboxes
  id: prepare_release_branch
  attributes:
    label: "**For all releases** Create `prepare-release/x.y.z` Branch"
    options:
      - label: "Ensure that you have up to date copy of `main`: `git checkout main; git pull` or a targeted release branch e.g. `release/2.7.x`: `git checkout release/2.7.x; git pull`"
      - label: "Create the `prepare-release` branch for the version (e.g. `prepare-release/2.7.1`): `git branch -m prepare-release/2.7.1`"
      - label: Make any final adjustments to CHANGELOG.md. Double-check that dates are correct, that link anchors point to the correct header, and that you've included a link to the Github compare link at the end. If there were any RC releases before this version, fold their changes into the final release entry.
      - label: Resolve all licensing issues that FOSSA has detected. Go to Issues tab in FOSSA's KIC project and resolve every issue, inspecting if it's a false positive or not. [ignored.go](https://github.com/Kong/team-k8s/blob/main/fossa/ignored.go) script should be useful to look for issues that have been already resolved and reappeared due to version changes.
      - label: Update [ignored.json](https://github.com/Kong/team-k8s/blob/main/fossa/kubernetes-ingress-controller/ignored.json) following instructions in [README](https://github.com/Kong/team-k8s/blob/main/fossa/README.md).
      - label: Retrieve the latest license report from FOSSA and save it to LICENSES (go to Reports tab in FOSSA's KIC project, select 'plain text' format, tick 'direct dependencies' and download it).
      - label: "Ensure base manifest versions use the new version (`config/image/enterprise/kustomization.yaml` and `config/image/oss/kustomization.yaml`) and update manifest files: `make manifests`"
      - label: "Push the branch up to the remote: `git push --set-upstream origin prepare-release/x.y.z`"
- type: checkboxes
  id: release_pr
  attributes:
    label: "**For all releases** Create a Release Pull Request"
    options:
      - label: Check the [latest E2E nightly test run](https://github.com/Kong/kubernetes-ingress-controller/actions/workflows/e2e_nightly.yaml) to confirm that E2E tests are succeeding. If you are backporting features into a non-main branch, run a [targeted E2E job against that branch](https://github.com/Kong/kubernetes-ingress-controller/actions/workflows/e2e_targeted.yaml) or use `ci/run-e2e` label on the PR preparing the release.
      - label: Open a PR from your branch to `main`. Set a `backport release/X.Y.Z` label.
      - label: If this is a patch release, ensure that the release branch (e.g. `release/2.9.x`) compared against the latest patch for this minor release (e.g. `v2.9.0`) includes the expected changes that the release should include (e.g. by checking [https://github.com/kong/kubernetes-ingress-controller/compare/v2.9.0..release/2.9.x](https://github.com/kong/kubernetes-ingress-controller/compare/v2.9.0..release/2.9.x)).
      - label: Once the PR is merged (the `prepare-release/x.y.z` branch will get automatically removed), approve and merge the automatic backport PR and [initiate a release job](https://github.com/Kong/kubernetes-ingress-controller/actions/workflows/release.yaml) on the `main` branch for major or minor release, for patch use the release branch. Your tag must use `vX.Y.Z` format. Set `latest` to true if this is be the latest release. That should be the case if a new major.minor release is done or a patch release is done on the latest minor version.
      - label: CI will validate the requested version, build and push an image, and run tests against the image before finally creating a tag and publishing a release. If tests fail, CI will push the image but not the tag or release. Investigate the failure, correct it as needed, and start a new release job.
      - label: The release workflow ([.github/workflows/release.yaml](/Kong/kubernetes-ingress-controller/blob/main/.github/workflows/release.yaml)) will update the `latest` branch - if the released version was set to be `latest` - to the just released tag.
- type: checkboxes
  id: release_documents
  attributes:
    label: "**For major/minor releases only** Update Release documents"
    options:
      - label: Trigger [release_docs](https://github.com/Kong/kubernetes-ingress-controller/blob/main/.github/workflows/release_docs.yaml) workflow. Note that you will need to update the new version's navigation manifest (e.g. [for 2.7](https://github.com/Kong/docs.konghq.com/blob/main/app/_data/docs_nav_kic_2.7.x.yml) to use the new files (CRDs and CLI arguments references) after.
      - label: Ensure a draft PR is created in [docs.konghq.com](https://github.com/Kong/docs.konghq.com/pulls) repository.
      - label: If you are adding a new CRD, add a new description file under `app/_includes/md/kic/crd-ref/`. This is a brief description injected into the CRD reference page.
      - label: Update articles in the new version as needed.
      - label: Update `references/version-compatibility.md` to include the new versions (make sure you capture any new Kubernetes/Istio versions that have been tested)
      - label: Copy `app/_data/docs_nav_kic_OLDVERSION.yml` to `app/_data/docs_nav_kic_NEWVERSION.yml` and update the `release` field to `NEWVERSION`. Add entries for any new articles.
      - label: Make sure that `app/_data/docs_nav_kic_NEWVERSION.yml` links to the latest generated `custom-resources-X.X.X.md`.
      - label: Add a section to `app/_data/kong_versions.yml` for your version.
      - label: "Add entries in support policy documents: `app/_includes/md/support-policy.md` and `app/_src/kubernetes-ingress-controller/support-policy.md`."
      - label: Mark the PR ready for review.
      - label: Inform and ping the @Kong/team-k8s via slack of impending release with a link to the release PR.
- type: textarea
  id: conformance_tests_report
  attributes:
    label: Conformance tests report
    value: Trigger for released version CI workflow `conformance_tests_report.yaml`, verify artifact and submit it to https://github.com/kubernetes-sigs/gateway-api/tree/main/conformance/reports. It's still in experimental phase. Update the KIC version in the README's Gateway API conformance badge.
- type: textarea
  id: release_trouble_shooting_link
  attributes:
    label: Release Troubleshooting
    value: The [Release Troubleshooting guide](https://github.com/Kong/kubernetes-ingress-controller/blob/main/RELEASE.md#release-troubleshooting) covers strategies for dealing with a release that has failed.
