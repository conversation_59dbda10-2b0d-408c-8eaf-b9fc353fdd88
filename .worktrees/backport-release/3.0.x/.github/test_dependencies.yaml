e2e:
  kind:
    # renovate: datasource=docker depName=kindest/node versioning=docker
    - 'v1.28.0'
    # renovate: datasource=docker depName=kindest/node@only-patch packageName=kindest/node versioning=docker
    - 'v1.27.3'
    # renovate: datasource=docker depName=kindest/node@only-patch packageName=kindest/node versioning=docker
    - 'v1.26.6'
    # renovate: datasource=docker depName=kindest/node@only-patch packageName=kindest/node versioning=docker
    - 'v1.25.11'
    # renovate: datasource=docker depName=kindest/node@only-patch packageName=kindest/node versioning=docker
    - 'v1.24.15'
  gke:
    # renovate: datasource=custom.gke-rapid depName=gke versioning=semver
    - '1.28.2'


  # For Istio, we define combinations of Kind and Istio versions that will be
  # used directly in the test matrix `include` section.
  istio:
    - # renovate: datasource=docker depName=kindest/node versioning=docker
      kind: 'v1.28.0'
      # renovate: datasource=docker depName=istio/istioctl versioning=docker
      istio: '1.19.3'
    - # renovate: datasource=docker depName=kindest/node@only-patch packageName=kindest/node versioning=docker
      kind: 'v1.27.3'
      # renovate: datasource=docker depName=istio/istioctl@only-patch packageName=istio/istioctl versioning=docker
      istio: '1.18.5'
    - # renovate: datasource=docker depName=kindest/node@only-patch packageName=kindest/node versioning=docker
      kind: 'v1.26.6'
      # renovate: datasource=docker depName=istio/istioctl@only-patch packageName=istio/istioctl versioning=docker
      istio: '1.17.8'
    - # renovate: datasource=docker depName=kindest/node@only-patch packageName=kindest/node versioning=docker
      kind: 'v1.25.11'
      # renovate: datasource=docker depName=istio/istioctl@only-patch packageName=istio/istioctl versioning=docker
      istio: '1.16.7'

integration:
  helm:
    # renovate: datasource=helm depName=kong registryUrl=https://charts.konghq.com versioning=helm
    kong: '2.30.0'
  # renovate: datasource=docker depName=kindest/node versioning=docker
  kind: 'v1.28.0'
  # renovate: datasource=docker depName=kong versioning=docker
  kong-oss: '3.4.2'
  # renovate: datasource=docker depName=kong/kong-gateway versioning=docker
  kong-ee: '3.4.1.1'

kongintegration:
  # renovate: datasource=docker depName=kong versioning=docker
  kong-oss: '3.4.2'
