name: Create release docs PR

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'The version to release (e.g. v1.2.3)'
        required: true

jobs:
  create_docs_pr:
    runs-on: ubuntu-latest
    steps:
      - name: Parse semver string
        id: semver_parser
        uses: booxmedialtd/ws-action-parse-semver@v1.4.7
        with:
          input_string: ${{ github.event.inputs.tag }}
          version_extractor_regex: 'v(.*)$'

      - name: Checkout KIC repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Checkout docs repository
        uses: actions/checkout@v4
        with:
          repository: kong/docs.konghq.com
          path: docs.konghq.com
          fetch-depth: 0

      - name: Generate CRDs reference
        run: |
          ./scripts/apidocs-gen/post-process-for-konghq.sh \
          docs.konghq.com/app/_src/kubernetes-ingress-controller/references/custom-resources-${{ steps.semver_parser.outputs.major }}.${{ steps.semver_parser.outputs.minor }}.x.md

      - name: Generate flags reference
        run: |
          ./scripts/cli-arguments-docs-gen/post-process-for-konghq.sh \
          docs.konghq.com/app/_src/kubernetes-ingress-controller/references/cli-arguments-${{ steps.semver_parser.outputs.major }}.${{ steps.semver_parser.outputs.minor }}.x.md

      - name: Detect changes
        id: detect-changes
        run: |
          if [[ `cd docs.konghq.com && git status --porcelain` ]]; then
            echo "Changes detected in docs repo"
            echo "HAS_CHANGES=true" >> $GITHUB_OUTPUT
          else
            echo "No changes detected in docs repo"
          fi

      - name: GPG sign the commits
        uses: crazy-max/ghaction-import-gpg@82a020f1f7f605c65dd2449b392a52c3fcfef7ef
        with:
          workdir: docs.konghq.com
          gpg_private_key: ${{ secrets.K8S_TEAM_BOT_GPG_PRIVATE_KEY }}
          passphrase: ${{ secrets.K8S_TEAM_BOT_GPG_PASSPHRASE }}
          git_user_signingkey: true
          git_commit_gpgsign: true

      - name: Create a PR in docs repo
        uses: peter-evans/create-pull-request@153407881ec5c347639a548ade7d8ad1d6740e38
        if: steps.detect-changes.outputs.HAS_CHANGES
        with:
          token: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}
          title: Update docs for KIC ${{ steps.semver_parser.outputs.fullversion }}
          commit-message: Synchronize auto-generated KIC API reference
          committer: Kong's Team k8s bot <<EMAIL>>
          author: Kong's Team k8s bot <<EMAIL>>
          signoff: true
          path: docs.konghq.com
          base: main
          branch: kic-docs-sync
          delete-branch: true
          draft: true
          labels: |
            Kubernetes Team
            review:autodoc
          body: Prepares documentation for KIC ${{ steps.semver_parser.outputs.fullversion }} release.
