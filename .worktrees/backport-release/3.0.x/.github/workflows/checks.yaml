name: Checks
run-name: Checks, branch:${{ github.ref_name }}, triggered by @${{ github.actor }}

concurrency:
  # Run only for most recent commit in PRs but for all tags and commits on main
  # Ref: https://docs.github.com/en/actions/using-jobs/using-concurrency
  group: ${{ github.workflow }}-${{ github.head_ref || github.sha }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - '**'
  push:
    branches:
      - 'main'
      - 'release/[0-9]+.[0-9]+.x'
    tags:
      - '**'
  workflow_dispatch: {}

jobs:
  up-to-date:
    runs-on: ubuntu-latest
    outputs:
      status: ${{ steps.up-to-date.outputs.status }}
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Check if PR is up to date, if it is skip workflows for this ref
        id: 'up-to-date'
        if: github.event_name == 'push' && startsWith(github.ref, 'refs/heads/')
        uses: Kong/public-shared-actions/pr-previews/up-to-date@v1.13.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

  # This job is used to check if the secrets are available. If they are not, we'll skip jobs that require them.
  should-run-with-secrets:
    runs-on: ubuntu-latest
    needs:
    - up-to-date
    if: needs.up-to-date.outputs.status != 'true'
    outputs:
      result: ${{ steps.check.outputs.result }}
    steps:
      - name: Check if secrets are available
        id: check
        run: |
          if [ "${{ secrets.PULP_PASSWORD }}" == "" ]; then
            echo "result=false" >> $GITHUB_OUTPUT
          else
            echo "result=true" >> $GITHUB_OUTPUT
          fi

  tools:
    runs-on: ubuntu-latest
    needs:
    - up-to-date
    if: needs.up-to-date.outputs.status != 'true'
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: setup golang
        uses: actions/setup-go@v4
        with:
          go-version-file: third_party/go.mod
      - run: make tools

  linters:
    needs:
    - up-to-date
    if: needs.up-to-date.outputs.status != 'true'
    uses: ./.github/workflows/_linters.yaml
    secrets: inherit

  unit-tests:
    needs:
    - up-to-date
    if: needs.up-to-date.outputs.status != 'true'
    uses: ./.github/workflows/_unit_tests.yaml
    secrets: inherit

  envtest-tests:
    needs:
    - up-to-date
    if: needs.up-to-date.outputs.status != 'true'
    uses: ./.github/workflows/_envtest_tests.yaml
    secrets: inherit

  kongintegration-tests:
    needs:
      - up-to-date
    if: needs.up-to-date.outputs.status != 'true'
    uses: ./.github/workflows/_kongintegration_tests.yaml
    secrets: inherit

  integration-tests:
    needs:
    - should-run-with-secrets
    - up-to-date
    if: needs.should-run-with-secrets.outputs.result == 'true' && needs.up-to-date.outputs.status != 'true'
    uses: ./.github/workflows/_integration_tests.yaml
    secrets: inherit

  conformance-tests:
    needs:
    - up-to-date
    if: needs.up-to-date.outputs.status != 'true'
    uses: ./.github/workflows/_conformance_tests.yaml
    secrets: inherit

  build-docker-image:
    needs:
    - up-to-date
    if: needs.up-to-date.outputs.status != 'true'
    uses: ./.github/workflows/_docker_build.yaml
    secrets: inherit

  # We need this step to fail the workflow if any of the previous steps failed or were cancelled.
  # It allows to use this particular job as a required check for PRs.
  # Ref: https://github.com/orgs/community/discussions/26822#discussioncomment-3305794
  passed:
    runs-on: ubuntu-latest
    needs:
      - up-to-date
      - tools
      - linters
      - unit-tests
      - envtest-tests
      - kongintegration-tests
      - integration-tests
      - conformance-tests
      - build-docker-image
    if: always()
    steps:
      - if: contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled')
        run: |
          echo "Some jobs failed or were cancelled."
          exit 1

  test-reports:
    needs:
      - should-run-with-secrets
      - unit-tests
      - envtest-tests
      - kongintegration-tests
      - integration-tests
      - conformance-tests
      - up-to-date
    if: always() && needs.should-run-with-secrets.outputs.result == 'true' && needs.up-to-date.outputs.status != 'true'
    uses: ./.github/workflows/_test_reports.yaml
    secrets: inherit
