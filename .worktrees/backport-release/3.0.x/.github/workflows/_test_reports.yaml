# Uploads test reports to Codecov and BuildPulse.
# The contract for Codecov is that all test reports are uploaded to the same "coverage" artifact location.
# The contract for BuildPulse is that all test reports are uploaded to the same "tests-report" artifact location.

name: test reports

on:
  workflow_call:
    inputs:
      coverage:
        description: Whether to upload coverage to Codecov.
        type: boolean
        default: true
      buildpulse:
        description: Whether to upload test reports to BuildPulse.
        type: boolean
        default: true

jobs:
  coverage:
    if: ${{ inputs.coverage && !cancelled() }}
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: collect test coverage artifacts
        id: download-coverage
        uses: actions/download-artifact@v3
        with:
          name: coverage
          path: coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          name: combined-coverage
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ${{ steps.download-coverage.outputs.download-path }}
          fail_ci_if_error: true
          verbose: true

  buildpulse-report:
    if: ${{ inputs.buildpulse && !cancelled() }}
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: download tests report
        id: download-coverage
        uses: actions/download-artifact@v3
        with:
          name: tests-report
          path: report

      - name: Upload test results to BuildPulse for flaky test detection
        if: ${{ !cancelled() }}
        uses: buildpulse/buildpulse-action@v0.11.0
        with:
          account: 962416
          repository: *********
          path: report/*.xml
          key: ${{ secrets.BUILDPULSE_ACCESS_KEY_ID }}
          secret: ${{ secrets.BUILDPULSE_SECRET_ACCESS_KEY }}
