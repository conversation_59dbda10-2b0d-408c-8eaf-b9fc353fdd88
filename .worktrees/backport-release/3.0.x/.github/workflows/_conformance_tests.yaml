name: conformance tests

on:
  workflow_call: {}

jobs:
  dependencies-versions:
    runs-on: ubuntu-latest
    outputs:
      helm-kong: ${{ steps.set-versions.outputs.helm-kong }}
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - id: set-versions
        name: Set versions
        run: |
          echo "helm-kong=$(yq -ojson -r '.integration.helm.kong' < .github/test_dependencies.yaml )" >> $GITHUB_OUTPUT

  conformance-tests:
    name: ${{ matrix.name }}
    runs-on: ubuntu-latest
    needs: dependencies-versions
    env:
      TEST_KONG_HELM_CHART_VERSION: ${{ needs.dependencies-versions.outputs.helm-kong }}
    strategy:
      fail-fast: false
      matrix:
        include:
        - name: conformance-tests-traditional-compatible-router
          expression_routes: "false"
        - name: "conformance-tests-expressions-router"
          expression_routes: "true"
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: setup golang
        uses: actions/setup-go@v4
        with:
          go-version-file: go.mod

      - name: run conformance tests
        run: make test.conformance
        env:
          JUNIT_REPORT: "conformance-tests.xml"
          KONG_TEST_EXPRESSION_ROUTES: ${{ matrix.expression_routes }}

      - name: collect test report
        if: ${{ always() }}
        uses: actions/upload-artifact@v3
        with:
          name: tests-report
          path: conformance-tests.xml
