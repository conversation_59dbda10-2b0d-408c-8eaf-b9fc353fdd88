name: cleanup

on:
  schedule:
    - cron: '*/30 * * * *'
  workflow_dispatch: {}

jobs:
  gcloud:
    environment: gcloud
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: setup golang
        uses: actions/setup-go@v4
        with:
          go-version-file: go.mod
      - name: cleanup orphaned test clusters
        run: go run ./hack/cleanup gke
        env:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
          GOOGLE_PROJECT: ${{ secrets.GOOGLE_PROJECT }}
          GOOGLE_LOCATION: ${{ secrets.GOOGLE_LOCATION }}

  konnect:
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: setup golang
        uses: actions/setup-go@v4
        with:
          go-version-file: go.mod
      - name: cleanup orphaned test clusters
        run: go run ./hack/cleanup konnect
        env:
          TEST_KONG_KONNECT_ACCESS_TOKEN: ${{ secrets.K8S_TEAM_KONNECT_ACCESS_TOKEN }}
