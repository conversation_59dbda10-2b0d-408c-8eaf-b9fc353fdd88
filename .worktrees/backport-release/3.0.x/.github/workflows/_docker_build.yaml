name: docker build

on:
  workflow_call:
    inputs:
      tag:
        description: The tag for the image to be used (e.g. `v2.9.0`). If not specified, `sha-<short SHA>` will be used.
        required: false
        type: string
      tag-latest:
        description: Whether to tag the image as `latest`.
        type: boolean
        default: false
      platforms:
        description: The platforms to build for.
        type: string
        default: linux/amd64
    outputs:
      image:
        description: The image name and tag.
        value: ${{ jobs.build.outputs.image }}

jobs:
  prepare-tags:
    runs-on: ubuntu-latest
    outputs:
      tags: ${{ steps.merge-tags.outputs.tags }}
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Parse semver string
        if: ${{ inputs.tag != '' }}
        id: parse-semver-tag
        uses: booxmedialtd/ws-action-parse-semver@v1.4.7
        with:
          input_string: ${{ inputs.tag }}
          version_extractor_regex: 'v(.*)$'

      - uses: benjlevesque/short-sha@v2.2
        id: short-sha

      - name: Add standard tag
        id: add-standard-tag
        run: |
          if [ -z "${{ inputs.tag }}" ]; then
            echo "tag=type=raw,value=sha-${{ steps.short-sha.outputs.sha }}" >> $GITHUB_OUTPUT
          else
            echo "tag=type=raw,value=${{ steps.parse-semver-tag.outputs.fullversion }}" >> $GITHUB_OUTPUT
          fi

      - name: Add major.minor tag
        id: add-major-minor-tag
        if: ${{ steps.parse-semver-tag.outputs.prerelease == '' }}
        run: |
          echo "tag=type=raw,value=${{ steps.parse-semver-tag.outputs.major }}.${{ steps.parse-semver-tag.outputs.minor }}" >> $GITHUB_OUTPUT

      - name: Merge tags
        id: merge-tags
        run: |
          echo "tags<<EOF" >> $GITHUB_OUTPUT
          if [ -n "${{ steps.major-minor-tag.outputs.tag }}" ]; then
            echo "${{ steps.add-major-minor-tag.outputs.tag }}" >> $GITHUB_OUTPUT
          fi
          echo "${{ steps.add-standard-tag.outputs.tag }}" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Print tags
        run: |
          echo "tags: ${{ steps.merge-tags.outputs.tags }}"

  build:
    runs-on: ubuntu-latest
    needs: prepare-tags
    outputs:
      image: kong/kubernetes-ingress-controller:${{ steps.meta.outputs.version }}
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5.0.0
        with:
          images: kong/kubernetes-ingress-controller
          flavor: |
            latest=${{ inputs.tag-latest }}
          tags: ${{ needs.prepare-tags.outputs.tags }}

      - name: Build
        id: docker-build-dockerhub
        uses: docker/build-push-action@v5
        with:
          push: false
          file: Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
          target: distroless
          platforms: ${{ inputs.platforms }}
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git

      # Build locally with outputs set to `type=docker,dest=/tmp/image.tar` to save the image as a `kic-image` artifact.
      - name: Build locally
        id: docker-build-local
        uses: docker/build-push-action@v5
        with:
          load: true
          file: Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
          target: distroless
          outputs: type=docker,dest=/tmp/image.tar
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git

      - name: Upload image artifact
        uses: actions/upload-artifact@v3
        with:
          name: kic-image
          path: /tmp/image.tar
