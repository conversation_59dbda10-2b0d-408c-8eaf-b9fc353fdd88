name: PRs labels check

on:
  pull_request_target:
    types: [opened, reopened, ready_for_review, labeled, unlabeled, synchronize]

jobs:
  label:
    runs-on: ubuntu-latest
    steps:
      - uses: pmalek/verify-pr-label-action@v1.4.5
        with:
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          invalid-labels: 'do not merge,on-hold'
          pull-request-number: '${{ github.event.pull_request.number }}'
          disable-reviews: true
