name: linters

on:
  workflow_call: {}

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup go
        uses: actions/setup-go@v4
        with:
          go-version-file: third_party/go.mod

      - name: Run lint
        run: make lint

      - name: Verify manifest consistency
        run: make verify.manifests

      - name: Verify generators consistency
        run: make verify.generators
