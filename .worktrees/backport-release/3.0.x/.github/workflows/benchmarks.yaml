name: Go Benchmarks
on:
  push:
    branches:
    - '**'

permissions:
  contents: write
  deployments: write

jobs:
  benchmark:
    name: Run Go benchmarks
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Setup Go
      uses: actions/setup-go@v4
      with:
        go-version-file: go.mod

    - name: Run benchmark
      run: make bench | tee bench.out

    - name: Store benchmark result
      uses: benchmark-action/github-action-benchmark@v1
      with:
        name: Go Benchmark
        tool: 'go'
        output-file-path: bench.out
        github-token: ${{ secrets.GITHUB_TOKEN }}
        auto-push: true
        # Show alert with commit comment on detecting possible performance regression
        alert-threshold: '200%'
        alert-comment-cc-users: '@Kong/k8s-maintainers'
        comment-always: false
        comment-on-alert: true

        # Enable Job Summary for PRs
        summary-always: true
        fail-on-alert: true
