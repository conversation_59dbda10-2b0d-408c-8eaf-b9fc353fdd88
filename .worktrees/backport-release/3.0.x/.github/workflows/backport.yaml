name: Backport
on:
  pull_request_target:
    types:
      - closed
      - labeled

jobs:
  backport:
    name: Backport
    runs-on: ubuntu-latest
    # Only react to merged PRs for security reasons.
    # See https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#pull_request_target.
    if: >
      github.event.pull_request.merged
      && (
        github.event.action == 'closed'
        || (
          github.event.action == 'labeled'
          && contains(github.event.label.name, 'backport')
        )
      )
    steps:
      - uses: tibdex/backport@v2
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
