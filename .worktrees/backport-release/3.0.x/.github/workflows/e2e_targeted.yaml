name: e2e tests (targeted)
run-name: e2e tests (targeted), branch:${{ github.ref_name }}, triggered by @${{ github.actor }}

concurrency:
  # Limit the concurrency of e2e tests to run only 1 workflow for ref (branch).
  # Ref: https://docs.github.com/en/actions/using-jobs/using-concurrency
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
    inputs:
      controller-image:
        description: KIC Docker image to test with. If empty, builds an image from the dispatch branch.
        type: string
        required: false
      run-gke:
        description: Run E2E tests on GKE as well as on Kind.
        type: boolean
        default: false
      run-istio:
        description: Run Istio E2E tests.
        type: boolean
        default: false
      all-supported-k8s-versions:
        description: Run tests against all supported Kubernetes versions. Otherwise, only against the latest one.
        type: boolean
        default: false
      pr-number:
        description: PR number to post a comment in. If empty, no comment is posted.
        type: string
        required: false

jobs:
  post-comment-in-pr:
    if: ${{ github.event.inputs.pr-number != '' }}
    runs-on: ubuntu-latest
    env:
      GH_TOKEN: ${{ secrets.K8S_TEAM_BOT_GH_PAT }}
      # URL is the current workflow's run URL.
      # Sadly this is not readily available in github's context.
      URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
      PR_NUMBER: ${{ github.event.inputs.pr-number }}
      RUN_GKE: ${{ inputs.run-gke }}
    steps:
      - uses: actions/checkout@v4
      - run: |
          if [ "${RUN_GKE}" == "true" ]; then
            MSG="E2E (targeted) tests with KIND-based and GKE-based clusters were started at ${URL}"
          else
            MSG="E2E (targeted) tests with KIND-based clusters were started at ${URL}"
          fi
          gh pr comment ${PR_NUMBER} --body "${MSG}"
      # Remove the 'ci/run-e2e' label from the PR to prevent the `e2e_trigger_via_label.yaml`
      # workflow from running the `e2e_targeted.yaml` again.
      - run: gh pr edit ${PR_NUMBER} --remove-label ci/run-e2e

  build-image:
    if: ${{ inputs.controller-image == '' }}
    uses: ./.github/workflows/_docker_build.yaml
    secrets: inherit

  # We need to pick an image to use for the tests. If the input specified one, we use that. Otherwise, we use the one
  # built by the previous job.
  choose-image:
    runs-on: ubuntu-latest
    if: ${{ !cancelled() }}
    needs: build-image
    outputs:
      image: ${{ steps.choose.outputs.image }}
    steps:
      - name: Choose image
        id: choose
        run: |
          if [ "${{ inputs.controller-image }}" == "" ]; then
            echo "image=${{ needs.build-image.outputs.image }}" >> $GITHUB_OUTPUT
          else
            echo "image=${{ inputs.controller-image }}" >> $GITHUB_OUTPUT
          fi

      - name: Print image
        run: echo "Using ${{ steps.choose.outputs.image }}"

  run:
    needs: choose-image
    if: ${{ !cancelled() }}
    uses: ./.github/workflows/_e2e_tests.yaml
    secrets: inherit
    with:
      kic-image: ${{ needs.choose-image.outputs.image }}
      load-local-image: ${{ inputs.controller-image == '' }}
      run-gke: ${{ inputs.run-gke }}
      run-istio: ${{ inputs.run-istio }}
      all-supported-k8s-versions: ${{ inputs.all-supported-k8s-versions }}

  run-unreleased-kong:
    needs: choose-image
    if: ${{ !cancelled() }}
    uses: ./.github/workflows/_e2e_tests.yaml
    secrets: inherit
    with:
      kic-image: ${{ needs.choose-image.outputs.image }}
      load-local-image: ${{ inputs.controller-image == '' }}
      kong-image: kong/kong-gateway-dev:nightly
      # these do not honor the inputs, as this job is intended to be a minimal
      # test against unreleased kong images, with the main run covering the
      # other test conditions
      all-supported-k8s-versions: false
      run-gke: false
      run-istio: false

  test-reports:
    needs: run
    uses: ./.github/workflows/_test_reports.yaml
    secrets: inherit
    with:
      coverage: false # E2E tests do not generate coverage reports
