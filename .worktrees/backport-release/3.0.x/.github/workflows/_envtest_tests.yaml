name: envtest tests

on:
  workflow_call: {}

jobs:
  envtest-tests:
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: setup golang
        uses: actions/setup-go@v4
        with:
          go-version-file: go.mod

      - name: run envtest tests
        run: make test.envtest
        env:
          GOTESTSUM_JUNITFILE: envtest-tests.xml

      - name: collect test coverage
        uses: actions/upload-artifact@v3
        with:
          name: coverage
          path: coverage.envtest.out

      - name: collect test report
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: tests-report
          path: envtest-tests.xml
