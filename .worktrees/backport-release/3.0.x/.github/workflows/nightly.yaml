name: nightly

on:
  schedule:
    - cron: '30 3 * * *'
  workflow_dispatch: {}

jobs:
  build-push-images:
    environment: 'Docker Push'
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Add standard tags
        id: tags-standard
        run: |
          echo 'TAGS_STANDARD<<EOF' >> $GITHUB_OUTPUT
          echo 'type=raw,value=nightly' >> $GITHUB_OUTPUT
          echo "type=raw,value={{date 'YYYY-MM-DD'}}" >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5.0.0
        with:
          images: kong/nightly-ingress-controller
          tags: ${{ steps.tags-standard.outputs.TAGS_STANDARD }}
      - name: Build binary
        id: docker_build_binary
        uses: docker/build-push-action@v5
        with:
          push: false
          file: Dockerfile
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
          target: builder
          platforms: linux/amd64, linux/arm64
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git
      - name: Build and push distroless image to DockerHub
        id: docker_build
        uses: docker/build-push-action@v5
        with:
          push: true
          file: Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=local,src=/tmp/.buildx-cache
          target: distroless
          platforms: linux/amd64, linux/arm64
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            REPO_INFO=https://github.com/${{ github.repository }}.git
