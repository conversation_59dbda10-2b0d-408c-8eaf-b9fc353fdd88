name: Generate Kubernetes Gateway API conformance tests report
run-name: "Generate Kubernetes Gateway API conformance tests report ${{ format('ref:{0}', github.event.inputs.tag) }}"

on:
  workflow_dispatch:
    inputs:
      tag:
        description: The version of code to checkout (e.g. v1.2.3 or commit hash)
        required: false
        default: main

jobs:
  dependencies-versions:
    runs-on: ubuntu-latest
    outputs:
      helm-kong: ${{ steps.set-versions.outputs.helm-kong }}
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
          ref: ${{ github.event.inputs.tag }}

      - id: set-versions
        name: Set versions
        run: |
          echo "helm-kong=$(yq -ojson -r '.integration.helm.kong' < .github/test_dependencies.yaml )" >> $GITHUB_OUTPUT

  generate-report:
    runs-on: ubuntu-latest
    needs: dependencies-versions
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
          ref: ${{ github.event.inputs.tag }}

      - name: setup golang
        uses: actions/setup-go@v4
        with:
          go-version-file: go.mod

      - name: Run conformance experimental tests
        env:
          TEST_KONG_HELM_CHART_VERSION: ${{ needs.dependencies-versions.outputs.helm-kong }}
        run: make test.conformance-experimental

      # Generated report should be submitted to
      # https://github.com/kubernetes-sigs/gateway-api/tree/main/conformance/reports
      # in future when experimental becomes stable autamate creating PR (add to release workflow).
      # See: https://github.com/Kong/kubernetes-ingress-controller/issues/4654
      - name: Collect conformance tests report
        uses: actions/upload-artifact@v3
        with:
          name: kong-kubernetes-ingress-controller.yaml
          path: kong-kubernetes-ingress-controller.yaml
