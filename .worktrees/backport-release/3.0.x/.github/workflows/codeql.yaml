name: codeql

concurrency:
  # Run only for most recent commit in PRs but for all tags and commits on main
  # Ref: https://docs.github.com/en/actions/using-jobs/using-concurrency
  group: ${{ github.workflow }}-${{ github.head_ref || github.sha }}
  cancel-in-progress: true

on:
  push:
    branches:
      - 'main'
  pull_request:
    branches:
      - 'main'
  schedule:
    - cron: '27 0 * * 4'
  workflow_dispatch: {}

jobs:
  analyze:
    name: analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      fail-fast: false
      matrix:
        language: [ 'go' ]
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    - name: Install Go
      uses: actions/setup-go@v4
      with:
        go-version-file: go.mod
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: ${{ matrix.language }}
    - name: Autobuild
      uses: github/codeql-action/autobuild@v2
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
