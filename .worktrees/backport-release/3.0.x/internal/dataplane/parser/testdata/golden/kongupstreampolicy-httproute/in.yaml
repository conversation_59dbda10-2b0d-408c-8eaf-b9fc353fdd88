---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: httpbin-prod
  name: httpbin-prod
  namespace: default
  annotations:
    konghq.com/upstream-policy: httpbin-httproute
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: httpbin-prod
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: httpbin-test
  name: httpbin-test
  namespace: default
  annotations:
    konghq.com/upstream-policy: httpbin-httproute
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: httpbin
  type: ClusterIP
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httpbin
  namespace: default
  annotations:
    konghq.com/strip-path: "true"
spec:
  parentRefs:
    - name: kong
  rules:
    - matches: # This rule will generate an Upstream with the name `httproute.default.httpbin.0`.
        - path:
            type: PathPrefix
            value: /httpbin-with-test
      backendRefs:
        - name: httpbin-prod
          kind: Service
          port: 80
          weight: 75
        - name: httpbin-test
          kind: Service
          port: 80
          weight: 25
    - matches: # This rule will generate an Upstream with the name `httproute.default.httpbin.1`.
        - path:
            type: PathPrefix
            value: /httpbin-prod-only
      backendRefs:
        - name: httpbin-prod
          kind: Service
          port: 80
---
# This policy will be applied to all Upstreams generated for the annotated Services:
# - httproute.default.httpbin.0
# - httproute.default.httpbin.1
apiVersion: configuration.konghq.com/v1beta1
kind: KongUpstreamPolicy
metadata:
  name: httpbin-httproute
  namespace: default
spec:
  algorithm: consistent-hashing
  slots: 100
  hashOn:
    header: session-id
  hashOnFallback:
    input: consumer
  healthchecks:
    active:
      type: http
      httpPath: /status
      httpsSni: example.com
      httpsVerifyCertificate: false
      concurrency: 20
      timeout: 15
      headers:
        X-Health-Check:
          - kong
          - dataplane
      healthy:
        httpStatuses: [200, 302]
        interval: 5
        successes: 5
      unhealthy:
        httpStatuses: [400, 500]
        httpFailures: 5
        timeouts: 5
        interval: 10
    passive:
      type: tcp
      healthy:
        successes: 5
      unhealthy:
        tcpFailures: 5
        timeouts: 10
