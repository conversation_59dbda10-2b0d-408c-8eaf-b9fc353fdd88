_format_version: "3.0"
plugins:
- config:
    header_name: kong-id
  instance_name: example-7d9f28edc
  name: correlation-id
  route: default.httpbin.httpbin..80
  tags:
  - k8s-name:kong-id
  - k8s-namespace:default
  - k8s-kind:KongPlugin
  - k8s-group:configuration.konghq.com
  - k8s-version:v1
- config:
    header_name: kong-id
  instance_name: example-76ac9ccd1
  name: correlation-id
  route: default.httpbin-other.httpbin..80
  tags:
  - k8s-name:kong-id
  - k8s-namespace:default
  - k8s-kind:KongPlugin
  - k8s-group:configuration.konghq.com
  - k8s-version:v1
- config:
    header_name: kong-id
  instance_name: example-b8b9400c0
  name: correlation-id
  route: default.httpbin-other.httpbin-other..80
  tags:
  - k8s-name:kong-id
  - k8s-namespace:default
  - k8s-kind:KongPlugin
  - k8s-group:configuration.konghq.com
  - k8s-version:v1
services:
- connect_timeout: 60000
  host: httpbin.default.80.svc
  id: 764d9f4d-c24c-5709-991c-19a96a581f6e
  name: default.httpbin.80
  path: /
  port: 80
  protocol: http
  read_timeout: 60000
  retries: 5
  routes:
  - https_redirect_status_code: 426
    id: 5df756b2-ceba-55e3-b9dc-a7e4dfbe6947
    name: default.httpbin.httpbin..80
    path_handling: v0
    paths:
    - /httpbin
    preserve_host: true
    protocols:
    - http
    - https
    regex_priority: 0
    request_buffering: true
    response_buffering: true
    strip_path: false
    tags:
    - k8s-name:httpbin
    - k8s-namespace:default
    - k8s-kind:Ingress
    - k8s-group:networking.k8s.io
    - k8s-version:v1
  - https_redirect_status_code: 426
    id: 5dbcc13e-ee70-5b2c-8ced-48c1454e32c4
    name: default.httpbin-other.httpbin..80
    path_handling: v0
    paths:
    - /httpbin-diff
    preserve_host: true
    protocols:
    - http
    - https
    regex_priority: 0
    request_buffering: true
    response_buffering: true
    strip_path: false
    tags:
    - k8s-name:httpbin-other
    - k8s-namespace:default
    - k8s-kind:Ingress
    - k8s-group:networking.k8s.io
    - k8s-version:v1
  tags:
  - k8s-name:httpbin
  - k8s-namespace:default
  - k8s-kind:Service
  - k8s-version:v1
  write_timeout: 60000
- connect_timeout: 60000
  host: httpbin-other.default.80.svc
  id: 98bf1a35-c9f4-5102-a0fd-3fce31982820
  name: default.httpbin-other.80
  path: /
  port: 80
  protocol: http
  read_timeout: 60000
  retries: 5
  routes:
  - https_redirect_status_code: 426
    id: 34b4d13c-2f70-5d47-a009-411826202ea2
    name: default.httpbin-other.httpbin-other..80
    path_handling: v0
    paths:
    - /httpbin-other
    preserve_host: true
    protocols:
    - http
    - https
    regex_priority: 0
    request_buffering: true
    response_buffering: true
    strip_path: false
    tags:
    - k8s-name:httpbin-other
    - k8s-namespace:default
    - k8s-kind:Ingress
    - k8s-group:networking.k8s.io
    - k8s-version:v1
  tags:
  - k8s-name:httpbin-other
  - k8s-namespace:default
  - k8s-kind:Service
  - k8s-version:v1
  write_timeout: 60000
upstreams:
- algorithm: round-robin
  name: httpbin.default.80.svc
  tags:
  - k8s-name:httpbin
  - k8s-namespace:default
  - k8s-kind:Service
  - k8s-version:v1
- algorithm: round-robin
  name: httpbin-other.default.80.svc
  tags:
  - k8s-name:httpbin-other
  - k8s-namespace:default
  - k8s-kind:Service
  - k8s-version:v1
