---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: kong
  name: ing-with-tls
  namespace: bar-namespace
  uid: c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
spec:
  rules:
  - host: example.com
    http:
      paths:
      - backend:
          service:
            name: foo-svc
            port:
              number: 80
        path: /
        pathType: Exact
  tls:
  - hosts:
    - 1.example.com
    - 2.example.com
    secretName: sooper-secret
  - hosts:
    - 3.example.com
    - 4.example.com
    secretName: sooper-secret2
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kubernetes.io/ingress.class: kong
  name: foo-svc
  namespace: bar-namespace
  uid: c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
spec:
  ports:
    - port: 80
---
apiVersion: v1
kind: Secret
metadata:
  name: sooper-secret
  namespace: bar-namespace
  uid: c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
type: kubernetes.io/tls
data:
  tls.key: |
    LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUNkUUlCQURBTkJna3Foa2lHOXcwQkFRRUZB
    QVNDQWw4d2dnSmJBZ0VBQW9HQkFMc2krZEtPRWNscUdDMEUKMU9jQ3BHRjNNZlp3MkU1L3VqdWxr
    RDNBOGpuejBKcHZkcjJyZ1N0OHpTVU1aNFN0NlhRaWJQNVl2cXRnYXB3VgptS0JYS2Z3eEV5SGpy
    c2lPTi9nYVFFWXdCNTdWRE5OcktIM0cwekVSZ052UDdnb0t2dU9pMGc2anhwZWkza2dOCmFYTzVy
    bk5aaEpwTDVKV0JjME9leG5udUl1VWhBZ01CQUFFQ2dZQVJwdnoxMVp6cjZPd2E0d2ZLT3IrU3lo
    R1cKYzVLVDVReUdMOW5wV1ZnQUMzV3orNnV4dkluVXRsTUxtWjN5TUEyRGZQUFhFanY2SW9BcjlR
    V09xbW8xVERvdQp2cGk3bjA2R2xUOHFPTVdPcGJQb1I3aENDYTRubHN4NDhxOFFRK0tubkNoejBB
    Z05ZdGxJdTlIMWwxYTIwSHV0Ci9xb0VXN1dlL0dQdGJIYkFBUUpCQVBjN3dWVUd0bUhpRnRYSTJO
    L2JkUmtlZms2N1RneXRNUVZVMWVISWhuaDgKZ2xBVnB1R05ZY3lYWW9EZm9kL3lNcElKNFRvMkZO
    Z1JOVmFIV2dmT2hRRUNRUURCeGJJdncrUEtydXJOSWJxcgpzdS9mY0RKWGRLWit3ZnVKSlgya1JR
    ZU1nYTBuVmNxTFVaVjFSQVBtQ2cwWXYrUU5ob3ZxMW91d0xOc1pLcGU1Cnc4QWhBa0JER2FHNExQ
    RTFFY0syMVNNZlpwV2FjcTgvT1JETzJmYVRCdHBoeENYUzc2QUNrazNQcTZxZWQzdlIKbEdCL3dt
    RTlSNWNzVUY5SjRTbkR5VXFERWVjQkFrQXZWV1NlaUdKM20xemQrUlJKWnU5empFdXYwMTNzYnVS
    TAo3eTJPMkJIcy82eFZoSDV5bzk0M2hBTFR5YmJEU2ZTaVhUQ0drQndWVUEvQlNRZEJLSkVoQWtB
    L1hTVjJKVGxlCmc1Umh4a3VEWnN0M0s4YXVwd1dLQzRFOXp1ZythcmFRa256ak1oNk1TbDZ1MitS
    TmlmUnJ6MmtUaFEzSFlqMGcKNUdUeWw3WEpteVkvCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0K
  tls.crt: |
    LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJvVENDQVFvQ0NRQy9WNU9mVFh1N3hEQU5C
    Z2txaGtpRzl3MEJBUXNGQURBVk1STXdFUVlEVlFRRERBcHIKYjI1bmFIRXVZMjl0TUI0WERUSXpN
    RFl3TVRFM05UQXdPRm9YRFRJME1EVXpNVEUzTlRBd09Gb3dGVEVUTUJFRwpBMVVFQXd3S2EyOXVa
    Mmh4TG1OdmJUQ0JuekFOQmdrcWhraUc5dzBCQVFFRkFBT0JqUUF3Z1lrQ2dZRUF1eUw1CjBvNFJ5
    V29ZTFFUVTV3S2tZWGN4OW5EWVRuKzZPNldRUGNEeU9mUFFtbTkydmF1Qkszek5KUXhuaEszcGRD
    SnMKL2xpK3EyQnFuQldZb0ZjcC9ERVRJZU91eUk0MytCcEFSakFIbnRVTTAyc29mY2JUTVJHQTI4
    L3VDZ3ErNDZMUwpEcVBHbDZMZVNBMXBjN211YzFtRW1rdmtsWUZ6UTU3R2VlNGk1U0VDQXdFQUFU
    QU5CZ2txaGtpRzl3MEJBUXNGCkFBT0JnUUJCdngwYmR5RmRXeGE3NVI5YnJ6OHMxR1lMclZtazd6
    Q1hzcHZ5OXNEeTFSb2FWN1RuWWRXeHYvSFUKOWZ1bXcrMFJvU3N4eXNRUmMxaVdBOHFKNnkycnEz
    RzdBM0dIdElQcXNESHJqb1M5czlZdEpvNGlUNElOSjNJbQowZkIwUURyMUY0RjVQNlRaeU11MVdq
    Z3QyQ2hlcWFaSDZUTGE4RW00RnovUXJmYzFBZz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
---
apiVersion: v1
kind: Secret
metadata:
  name: sooper-secret2
  namespace: bar-namespace
  uid: 8aade13c-1470-46bd-9849-9a74e349214f
type: kubernetes.io/tls
data:
  tls.key: |
    LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUNkd0lCQURBTkJna3Foa2lHOXcwQkFRRUZB
    QVNDQW1Fd2dnSmRBZ0VBQW9HQkFMZzdDY1pBdmtGaHcvRkgKendZNjFGNXpPdllxdkZVYzh5bFpj
    VktKdS81bDArRm0wKzd5eUcrcVZybGhpVjhEK3lGSG5DMGdCVThEQi9jYQpuZ2ZpSENNRGxVY2tj
    RTdQTHdhOGZxelZ3VXU0QzhnTFduaG0xcHlwZmdQZVNpK1k1MmhCdms2OFRCcVdaVVVxCkl6bHJI
    UDdCUDJMZmxxaHlmVUtpaXI0Q2hGTFJBZ01CQUFFQ2dZQkJKYWRzTXVMVWJnVURJbmlENUhQS3Zv
    YkEKSEJhakpteWFWNldxSVVpWVNsdmxuaDRkcGo3WXRleWErM0cvWnNIOFg3MUxlOEpFMFhhVW5C
    WDhCYW9hNGdXaQpXczZDVFg1eUhWTWloSjNJeGg1ZEd0NnBkS1BUQ2JCZ05mKzB5U1ZhdmJDRGhm
    TGlzV1RNTnVONkt4SG14MEdXClBZODVSdXpSTGw2OCtoN1ZFUUpCQU5yUEpFMk1BeFRwWitRdzJy
    MmpQRjV5ZjFuand1eWlJa1pObHF6N1ZtdnkKWkNYQlpmYWhVWFBjUjVrYTR4S09MZ2M3b0NhaCs0
    M2dORG1xMHZEdEVQMENRUURYaTA5WEp5TGpuRDFScFZJaQp0L2NHNUppREJ2dHVTR2pJN3MvbUky
    VHMwZnBvL1UzNVdVR2FqSGFnbWE5YzJxbFlNZVZaN2Z1cHhMTmFMRDZ6CjFNdGxBa0VBMWgvN3dM
    K1JqSGRWS2VQOVM3TmdzblNOMStPaHIzeUMyaFczckJSUjRGVldWL1JJMmUvSUMvKzMKT1Vjc2k4
    NERrU1J5ZHh2eFZrZmdFOGJ0b3NQNzZRSkFCak1XbEI0bkRiN25zSnA5czB2eFNmeDNPb1dQNDhz
    bgpZR2dtQ0t1SjhwblRod09LSTVyaW5TeGZHUjF5Z3N3elJMc2lxcVNDc1k1YnprTXBob2lmVlFK
    QkFJVW1yWFBqCjUxb2QzazVWcFdvMVMrTWc5SWl1ZnJucXRwRnUva0VFWGx1eENxcnNrYjFDN21X
    TWtHcXkwYkhwQTAxNFNxdTIKN0lua2tSb0RuVHJVM1JvPQotLS0tLUVORCBQUklWQVRFIEtFWS0t
    LS0tCg==
  tls.crt: |
    LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJvVENDQVFvQ0NRQ2pacTJsNklHQUFUQU5C
    Z2txaGtpRzl3MEJBUXNGQURBVk1STXdFUVlEVlFRRERBcHIKYjI1bmFIRXVZMjl0TUI0WERUSXpN
    RFl3TVRFM05UTXlNVm9YRFRJME1EVXpNVEUzTlRNeU1Wb3dGVEVUTUJFRwpBMVVFQXd3S2EyOXVa
    Mmh4TG1OdmJUQ0JuekFOQmdrcWhraUc5dzBCQVFFRkFBT0JqUUF3Z1lrQ2dZRUF1RHNKCnhrQytR
    V0hEOFVmUEJqclVYbk02OWlxOFZSenpLVmx4VW9tNy9tWFQ0V2JUN3ZMSWI2cFd1V0dKWHdQN0lV
    ZWMKTFNBRlR3TUg5eHFlQitJY0l3T1ZSeVJ3VHM4dkJyeCtyTlhCUzdnTHlBdGFlR2JXbktsK0E5
    NUtMNWpuYUVHKwpUcnhNR3BabFJTb2pPV3NjL3NFL1l0K1dxSEo5UXFLS3ZnS0VVdEVDQXdFQUFU
    QU5CZ2txaGtpRzl3MEJBUXNGCkFBT0JnUUNlZVhHcUZpT3BQZTRFdUZFNE8veWR2T01tdW44YXAz
    ZTBWTkd3V3U3eWxOMmJ4Z2dLM0l2WjVyTmYKMDNVYzhlMWVvS1dhMzBna3cxdHlRUTB1bzMzVmJ4
    ekpWcmJNV1FHN3N5WU1vY2d0LzhDemc4clg0ZXpDT3N0SgpQWFhKUEdTLzNPN0pPQ1ViQlVaMmFK
    N2VWSHpNTnluN0NpWmlxSTlrSnFFR2xVL0k4dz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
