apiVersion: configuration.konghq.com/v1beta1
kind: KongConsumerGroup
metadata:
  name: consumer-group-2
  annotations:
    kubernetes.io/ingress.class: kong
---
apiVersion: configuration.konghq.com/v1beta1
kind: KongConsumerGroup
metadata:
  name: consumer-group-1
  annotations:
    kubernetes.io/ingress.class: kong
---
apiVersion: configuration.konghq.com/v1
kind: KongConsumer
metadata:
  name: consumer-1
  annotations:
    kubernetes.io/ingress.class: kong
username: consumer-1
consumerGroups:
  - consumer-group-1
---
apiVersion: configuration.konghq.com/v1
kind: KongConsumer
metadata:
  name: consumer-2
  annotations:
    kubernetes.io/ingress.class: kong
username: consumer-2
consumerGroups:
  - consumer-group-1
  - consumer-group-2
