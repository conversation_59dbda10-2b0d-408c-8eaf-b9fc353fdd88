_format_version: "3.0"
services:
- connect_timeout: 60000
  host: httproute.default.test.0
  id: 5879d21c-33ed-5355-be10-f0911e04d397
  name: httproute.default.test.0
  port: 80
  protocol: http
  read_timeout: 60000
  retries: 5
  routes:
  - https_redirect_status_code: 426
    id: f78c2a62-d9bc-5b80-a16a-d19f4dea4fbe
    name: httproute.default.test.0.0
    path_handling: v0
    paths:
    - ~/test$
    - /test/
    preserve_host: true
    protocols:
    - http
    - https
    strip_path: true
    tags:
    - k8s-name:test
    - k8s-namespace:default
    - k8s-kind:HTTPRoute
    - k8s-group:gateway.networking.k8s.io
    - k8s-version:v1
  tags:
  - k8s-name:test
  - k8s-namespace:default
  - k8s-kind:HTTPRoute
  - k8s-group:gateway.networking.k8s.io
  - k8s-version:v1
  write_timeout: 60000
upstreams:
- algorithm: round-robin
  name: httproute.default.test.0
  tags:
  - k8s-name:test
  - k8s-namespace:default
  - k8s-kind:HTTPRoute
  - k8s-group:gateway.networking.k8s.io
  - k8s-version:v1
  targets:
  - target: **********:9443
    weight: 50
  - target: **********:9443
    weight: 50
