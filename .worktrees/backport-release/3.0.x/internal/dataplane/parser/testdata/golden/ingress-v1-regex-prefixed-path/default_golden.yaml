_format_version: "3.0"
services:
- connect_timeout: 60000
  host: foo-svc.foo-namespace.80.svc
  id: fe1e2edc-5479-52fe-b0f4-b90d8d5f83ba
  name: foo-namespace.foo-svc.80
  path: /
  port: 80
  protocol: http
  read_timeout: 60000
  retries: 5
  routes:
  - hosts:
    - example.com
    https_redirect_status_code: 426
    id: 45f1e9e4-8096-5cf7-b8e0-c42f8b9b81a0
    name: foo-namespace.regex-prefix.foo-svc.example.com.80
    path_handling: v0
    paths:
    - ~/foo/\d{3}
    preserve_host: true
    protocols:
    - http
    - https
    regex_priority: 0
    request_buffering: true
    response_buffering: true
    strip_path: false
    tags:
    - k8s-name:regex-prefix
    - k8s-namespace:foo-namespace
    - k8s-kind:Ingress
    - k8s-group:networking.k8s.io
    - k8s-version:v1
  tags:
  - k8s-name:foo-svc
  - k8s-namespace:foo-namespace
  - k8s-kind:Service
  - k8s-version:v1
  write_timeout: 60000
upstreams:
- algorithm: round-robin
  name: foo-svc.foo-namespace.80.svc
  tags:
  - k8s-name:foo-svc
  - k8s-namespace:foo-namespace
  - k8s-kind:Service
  - k8s-version:v1
