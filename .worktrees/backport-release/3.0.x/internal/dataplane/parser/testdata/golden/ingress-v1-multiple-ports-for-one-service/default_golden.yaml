_format_version: "3.0"
services:
- connect_timeout: 60000
  host: foo-svc.foo-namespace.8000.svc
  id: 8c1ded2c-3f68-5845-bc85-f4c4c318829a
  name: foo-namespace.foo-svc.8000
  path: /
  port: 8000
  protocol: http
  read_timeout: 60000
  retries: 5
  routes:
  - hosts:
    - example.net
    https_redirect_status_code: 426
    id: 2bd1c902-4d3c-50ed-9a78-f698cfa0cf37
    name: foo-namespace.foo.foo-svc.example.net.8000
    path_handling: v0
    paths:
    - /
    preserve_host: true
    protocols:
    - http
    - https
    regex_priority: 0
    request_buffering: true
    response_buffering: true
    strip_path: false
    tags:
    - k8s-name:foo
    - k8s-namespace:foo-namespace
    - k8s-kind:Ingress
    - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
    - k8s-group:networking.k8s.io
    - k8s-version:v1
  tags:
  - k8s-name:foo-svc
  - k8s-namespace:foo-namespace
  - k8s-kind:Service
  - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
  - k8s-version:v1
  write_timeout: 60000
- connect_timeout: 60000
  host: foo-svc.foo-namespace.80.svc
  id: fe1e2edc-5479-52fe-b0f4-b90d8d5f83ba
  name: foo-namespace.foo-svc.80
  path: /
  port: 80
  protocol: http
  read_timeout: 60000
  retries: 5
  routes:
  - hosts:
    - example.com
    https_redirect_status_code: 426
    id: 3eee2c18-8fcc-5661-8f84-5c89adfa404f
    name: foo-namespace.foo.foo-svc.example.com.80
    path_handling: v0
    paths:
    - /
    preserve_host: true
    protocols:
    - http
    - https
    regex_priority: 0
    request_buffering: true
    response_buffering: true
    strip_path: false
    tags:
    - k8s-name:foo
    - k8s-namespace:foo-namespace
    - k8s-kind:Ingress
    - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
    - k8s-group:networking.k8s.io
    - k8s-version:v1
  tags:
  - k8s-name:foo-svc
  - k8s-namespace:foo-namespace
  - k8s-kind:Service
  - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
  - k8s-version:v1
  write_timeout: 60000
upstreams:
- algorithm: round-robin
  name: foo-svc.foo-namespace.8000.svc
  tags:
  - k8s-name:foo-svc
  - k8s-namespace:foo-namespace
  - k8s-kind:Service
  - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
  - k8s-version:v1
- algorithm: round-robin
  name: foo-svc.foo-namespace.80.svc
  tags:
  - k8s-name:foo-svc
  - k8s-namespace:foo-namespace
  - k8s-kind:Service
  - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
  - k8s-version:v1
