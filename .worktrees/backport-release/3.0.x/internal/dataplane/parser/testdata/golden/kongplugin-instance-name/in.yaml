---
apiVersion: v1
kind: Service
metadata:
  name: httpbin
  namespace: default
  labels:
    app: httpbin
spec:
  ports:
  - name: http
    port: 80
    targetPort: 80
  selector:
    app: httpbin
---
apiVersion: v1
kind: Service
metadata:
  name: httpbin-other
  namespace: default
  labels:
    app: httpbin
spec:
  ports:
  - name: http
    port: 80
    targetPort: 80
  selector:
    app: httpbin
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: httpbin
  namespace: default
  annotations:
    konghq.com/plugins: kong-id
spec:
  ingressClassName: kong
  rules:
  - http:
      paths:
      - path: /httpbin
        pathType: ImplementationSpecific
        backend:
          service:
            name: httpbin
            port:
              number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: httpbin-other
  namespace: default
  annotations:
    konghq.com/plugins: kong-id
spec:
  ingressClassName: kong
  rules:
  - http:
      paths:
      - path: /httpbin-diff
        pathType: ImplementationSpecific
        backend:
          service:
            name: httpbin
            port:
              number: 80
      - path: /httpbin-other
        pathType: ImplementationSpecific
        backend:
          service:
            name: httpbin-other
            port:
              number: 80
---
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: kong-id
  namespace: default
config:
  header_name: kong-id
plugin: correlation-id
instance_name: example
