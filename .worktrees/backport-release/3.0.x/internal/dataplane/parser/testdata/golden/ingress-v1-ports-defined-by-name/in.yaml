---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: kong
  name: regex-prefix
  namespace: foo-namespace
spec:
  rules:
  - host: example.com
    http:
      paths:
      - backend:
          service:
            name: foo-svc
            port:
              name: http
        path: /
        pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kubernetes.io/ingress.class: kong
  name: foo-svc
  namespace: foo-namespace
spec:
  ports:
    - port: 80
      name: http
