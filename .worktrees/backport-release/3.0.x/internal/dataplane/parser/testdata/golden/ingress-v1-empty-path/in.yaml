---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: foo
  namespace: foo-namespace
spec:
  ingressClassName: kong
  rules:
  - host: example.com
    http:
      paths:
      - backend:
          service:
            name: foo-svc
            port:
              number: 80
        pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kubernetes.io/ingress.class: kong
  name: foo-svc
  namespace: foo-namespace
spec:
  ports:
    - port: 80
