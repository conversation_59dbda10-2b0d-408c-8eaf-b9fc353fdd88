_format_version: "3.0"
services:
- connect_timeout: 60000
  host: grpcroute.default.grpcbin.example.com.0
  id: e3750232-74d7-5496-bf14-1c266ec17184
  name: grpcroute.default.grpcbin.example.com.0
  plugins:
  - config:
      message: no existing backendRef provided
      status_code: 500
    name: request-termination
  protocol: grpcs
  read_timeout: 60000
  retries: 5
  routes:
  - expression: (http.path == "/grpcbin.GRPCBin/DummyUnary") && (http.host == "example.com")
    https_redirect_status_code: 426
    id: b23f135f-8d7e-54cb-96eb-f2579ef1608b
    name: grpcroute.default.grpcbin.example.com.0.0
    preserve_host: true
    priority: 26766487929087
    tags:
    - k8s-name:grpcbin
    - k8s-namespace:default
    - k8s-kind:GRPCRoute
    - k8s-group:gateway.networking.k8s.io
    - k8s-version:v1alpha2
  tags:
  - k8s-name:UNKNOWN
  - k8s-namespace:UNKNOWN
  - k8s-kind:Service
  - k8s-uid:00000000-0000-0000-0000-000000000000
  - k8s-group:core
  - k8s-version:v1
  write_timeout: 60000
upstreams:
- algorithm: round-robin
  name: grpcroute.default.grpcbin.example.com.0
  tags:
  - k8s-name:UNKNOWN
  - k8s-namespace:UNKNOWN
  - k8s-kind:Service
  - k8s-uid:00000000-0000-0000-0000-000000000000
  - k8s-group:core
  - k8s-version:v1
