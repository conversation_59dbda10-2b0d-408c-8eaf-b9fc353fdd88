_format_version: "3.0"
certificates:
- cert: |-
    -----B<PERSON><PERSON> CERTIFICATE-----
    MIIBoTCCAQoCCQCjZq2l6IGAATANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApr
    b25naHEuY29tMB4XDTIzMDYwMTE3NTMyMVoXDTI0MDUzMTE3NTMyMVowFTETMBEG
    A1UEAwwKa29uZ2hxLmNvbTCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAuDsJ
    xkC+QWHD8UfPBjrUXnM69iq8VRzzKVlxUom7/mXT4WbT7vLIb6pWuWGJXwP7IUec
    LSAFTwMH9xqeB+IcIwOVRyRwTs8vBrx+rNXBS7gLyAtaeGbWnKl+A95KL5jnaEG+
    TrxMGpZlRSojOWsc/sE/Yt+WqHJ9QqKKvgKEUtECAwEAATANBgkqhkiG9w0BAQsF
    AAOBgQCeeXGqFiOpPe4EuFE4O/ydvOMmun8ap3e0VNGwWu7ylN2bxggK3IvZ5rNf
    03Uc8e1eoKWa30gkw1tyQQ0uo33VbxzJVrbMWQG7syYMocgt/8Czg8rX4ezCOstJ
    PXXJPGS/3O7JOCUbBUZ2aJ7eVHzMNyn7CiZiqI9kJqEGlU/I8w==
    -----END CERTIFICATE-----
  id: 8aade13c-1470-46bd-9849-9a74e349214f
  key: |-
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  snis:
  - name: 3.example.com
  - name: 4.example.com
- cert: |-
    -----BEGIN CERTIFICATE-----
    MIIBoTCCAQoCCQC/V5OfTXu7xDANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApr
    b25naHEuY29tMB4XDTIzMDYwMTE3NTAwOFoXDTI0MDUzMTE3NTAwOFowFTETMBEG
    A1UEAwwKa29uZ2hxLmNvbTCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAuyL5
    0o4RyWoYLQTU5wKkYXcx9nDYTn+6O6WQPcDyOfPQmm92vauBK3zNJQxnhK3pdCJs
    /li+q2BqnBWYoFcp/DETIeOuyI43+BpARjAHntUM02sofcbTMRGA28/uCgq+46LS
    DqPGl6LeSA1pc7muc1mEmkvklYFzQ57Gee4i5SECAwEAATANBgkqhkiG9w0BAQsF
    AAOBgQBBvx0bdyFdWxa75R9brz8s1GYLrVmk7zCXspvy9sDy1RoaV7TnYdWxv/HU
    9fumw+0RoSsxysQRc1iWA8qJ6y2rq3G7A3GHtIPqsDHrjoS9s9YtJo4iT4INJ3Im
    0fB0QDr1F4F5P6TZyMu1Wjgt2CheqaZH6TLa8Em4Fz/Qrfc1Ag==
    -----END CERTIFICATE-----
  id: c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
  key: |-
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  snis:
  - name: 1.example.com
  - name: 2.example.com
services:
- connect_timeout: 60000
  host: foo-svc.bar-namespace.80.svc
  id: edc1d53e-73b4-5932-9455-0e01f0d53e3f
  name: bar-namespace.foo-svc.80
  path: /
  port: 80
  protocol: http
  read_timeout: 60000
  retries: 5
  routes:
  - expression: (http.host == "example.com") && (http.path == "/")
    https_redirect_status_code: 426
    id: fc9a8135-0253-5631-b1e0-8712f796a4e2
    name: bar-namespace.ing-with-tls.foo-svc.example.com.80
    preserve_host: true
    priority: 57178899677185
    request_buffering: true
    response_buffering: true
    strip_path: false
    tags:
    - k8s-name:ing-with-tls
    - k8s-namespace:bar-namespace
    - k8s-kind:Ingress
    - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
    - k8s-group:networking.k8s.io
    - k8s-version:v1
  tags:
  - k8s-name:foo-svc
  - k8s-namespace:bar-namespace
  - k8s-kind:Service
  - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
  - k8s-version:v1
  write_timeout: 60000
upstreams:
- algorithm: round-robin
  name: foo-svc.bar-namespace.80.svc
  tags:
  - k8s-name:foo-svc
  - k8s-namespace:bar-namespace
  - k8s-kind:Service
  - k8s-uid:c6ac927c-4f5a-4e88-8b5d-c7b01d0f43af
  - k8s-version:v1
