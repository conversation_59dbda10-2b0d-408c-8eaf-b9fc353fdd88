apiVersion: gateway.networking.k8s.io/v1alpha2
kind: GRPCRoute
metadata:
  name: grpcbin
  namespace: default
spec:
  parentRefs:
  - name: kong
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: grpcbin
      port: 443
    matches:
    - method:
        service: "grpcbin.GRPCBin"
        method: "DummyUnary"
---
apiVersion: v1
kind: Service
metadata:
  name: grpcbin
  namespace: default
  labels:
    app: grpcbin
spec:
  ports:
  - name: grpc
    port: 443
    targetPort: 9001
  selector:
    app: grpcbin