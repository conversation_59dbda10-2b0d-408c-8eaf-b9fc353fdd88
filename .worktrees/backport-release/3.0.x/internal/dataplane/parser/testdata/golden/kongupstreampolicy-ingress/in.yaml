---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: httpbin
  name: httpbin-deployment
  namespace: default
  annotations:
    konghq.com/upstream-policy: httpbin
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
    - port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    app: httpbin
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: httpbin-ingress-1
  namespace: default
  annotations:
    konghq.com/strip-path: "true"
spec:
  ingressClassName: kong
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: httpbin-deployment
                port:
                  number: 80
          - path: /internal
            pathType: Prefix
            backend:
              service:
                name: httpbin-deployment
                port:
                  number: 8080
---
# This policy will be applied to every Upstream that is created for
# the httpbin-deployment Service. In this case those would be following Kong Upstreams:
# - default.httpbin-deployment.80
# - default.httpbin-deployment.8080
apiVersion: configuration.konghq.com/v1beta1
kind: KongUpstreamPolicy
metadata:
  name: httpbin
  namespace: default
spec:
  algorithm: consistent-hashing
  slots: 100
  hashOn:
    header: session-id
  hashOnFallback:
    input: consumer
  healthchecks:
    active:
      type: http
      httpPath: /status
      httpsSni: example.com
      httpsVerifyCertificate: false
      concurrency: 20
      timeout: 15
      headers:
        X-Health-Check:
          - kong
          - dataplane
      healthy:
        httpStatuses: [200, 302]
        interval: 5
        successes: 5
      unhealthy:
        httpStatuses: [400, 500]
        httpFailures: 5
        timeouts: 5
        interval: 10
    passive:
      type: tcp
      healthy:
        successes: 5
      unhealthy:
        tcpFailures: 5
        timeouts: 10
