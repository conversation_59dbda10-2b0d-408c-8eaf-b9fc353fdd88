_format_version: "3.0"
services:
- connect_timeout: 60000
  host: grpcroute.default.grpcbin.0
  id: 21a5e729-c47e-5086-a236-0551b9a11bda
  name: grpcroute.default.grpcbin.0
  plugins:
  - config:
      message: no existing backendRef provided
      status_code: 500
    name: request-termination
  protocol: grpcs
  read_timeout: 60000
  retries: 5
  routes:
  - hosts:
    - example.com
    https_redirect_status_code: 426
    id: c28a0082-bf9d-5577-bd96-c82519503d53
    name: grpcroute.default.grpcbin.0.0
    path_handling: v0
    paths:
    - ~/grpcbin.GRPCBin/DummyUnary
    protocols:
    - grpc
    - grpcs
  tags:
  - k8s-name:UNKNOWN
  - k8s-namespace:UNKNOWN
  - k8s-kind:Service
  - k8s-uid:00000000-0000-0000-0000-000000000000
  - k8s-group:core
  - k8s-version:v1
  write_timeout: 60000
upstreams:
- algorithm: round-robin
  name: grpcroute.default.grpcbin.0
  tags:
  - k8s-name:UNKNOWN
  - k8s-namespace:UNKNOWN
  - k8s-kind:Service
  - k8s-uid:00000000-0000-0000-0000-000000000000
  - k8s-group:core
  - k8s-version:v1
