// This code was initially generated by github.com/deepmap/oapi-codegen version v1.12.4. Later it was adapted manually
// to support github.com/oapi-codegen/runtime types.
//
// It's no longer possible to regenerate this from source therefore it's maintained manually
// until we have a proper Konnect API Go SDK: https://github.com/Kong/kubernetes-ingress-controller/issues/3550.

//nolint:all
package controlplanes

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	AccessTokenScopes         = "accessToken.Scopes"
	PersonalAccessTokenScopes = "personalAccessToken.Scopes"
)

// Defines values for Status400.
const (
	N400 Status400 = 400
)

// Defines values for Status401.
const (
	N401 Status401 = 401
)

// Defines values for Status403.
const (
	N403 Status403 = 403
)

// Defines values for Status404.
const (
	N404 Status404 = 404
)

// Defines values for Status409.
const (
	N409 Status409 = 409
)

// Defines values for Status500.
const (
	N500 Status500 = 500
)

// Defines values for Status503.
const (
	N503 Status503 = 503
)

type ClusterType string

const (
	ClusterTypeKubernetesIngressController ClusterType = "CLUSTER_TYPE_K8S_INGRESS_CONTROLLER"
)

// AdditionalErrorInformation An array of objects that contains information related to the error response.
type AdditionalErrorInformation = []AdditionalErrorInformation_Item

// AdditionalErrorInformation_Item defines model for AdditionalErrorInformation.Item.
type AdditionalErrorInformation_Item struct {
	Field                *string                `json:"field,omitempty"`
	Reason               *string                `json:"reason,omitempty"`
	Rule                 *string                `json:"rule,omitempty"`
	AdditionalProperties map[string]interface{} `json:"-"`
}

// CreateControlPlaneRequest The request schema for the create control plane request.
type CreateControlPlaneRequest struct {
	// Description The description of the control plane in Konnect.
	Description *string `json:"description,omitempty"`

	// Labels Labels to facilitate tagged search on control planes. Keys must be of length 1-63 characters, and cannot start with 'kong', 'konnect', 'mesh', 'kic', or '_'.
	Labels *Labels `json:"labels,omitempty"`

	// Name The name of the control plane.
	Name string `json:"name"`

	// ClusterType is the type of cluster this control plane is associated with.
	ClusterType ClusterType `json:"cluster_type,omitempty"`
}

// Labels Labels to facilitate tagged search on control planes. Keys must be of length 1-63 characters, and cannot start with 'kong', 'konnect', 'mesh', 'kic', or '_'.
type Labels map[string]string

// PaginatedMeta Returns pagination information
type PaginatedMeta struct {
	// Page Contains pagination query parameters and the total number of objects returned.
	Page *struct {
		// Number Specifies the number of objects returned per page.
		Number *int `json:"number,omitempty"`

		// Size Specifies the size of the page.
		Size *int `json:"size,omitempty"`

		// Total Integer representation of the amount of items returned.
		Total *int `json:"total,omitempty"`
	} `json:"page,omitempty"`
}

// ControlPlane The control plane object contains information about a Kong control plane.
type ControlPlane struct {
	// Config CP configuration object for related access endpoints.
	Config *struct {
		// ControlPlaneEndpoint Control Plane Endpoint.
		ControlPlaneEndpoint *string `json:"control_plane_endpoint,omitempty"`

		// TelemetryEndpoint Telemetry Endpoint.
		TelemetryEndpoint *string `json:"telemetry_endpoint,omitempty"`
	} `json:"config,omitempty"`

	// CreatedAt An ISO-8604 timestamp representation of control plane creation date.
	CreatedAt *time.Time `json:"created_at,omitempty"`

	// Description The description of the control plane in Konnect.
	Description *string `json:"description,omitempty"`

	// Id The control plane ID.
	Id *openapi_types.UUID `json:"id,omitempty"`

	// Labels Labels to facilitate tagged search on control planes. Keys must be of length 1-63 characters, and cannot start with 'kong', 'konnect', 'mesh', 'kic', or '_'.
	Labels *Labels `json:"labels,omitempty"`

	// Name The name of the control plane.
	Name *string `json:"name,omitempty"`

	// UpdatedAt An ISO-8604 timestamp representation of control plane update date.
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
}

// Status400 The HTTP response code
type Status400 int

// Status401 The HTTP status code.
type Status401 int

// Status403 The HTTP status code.
type Status403 int

// Status404 The HTTP status code.
type Status404 int

// Status409 The HTTP status code.
type Status409 int

// Status500 The HTTP status code.
type Status500 int

// Status503 The HTTP status code.
type Status503 int

// UpdateControlPlaneRequest The request schema for the update control plane request.
type UpdateControlPlaneRequest struct {
	// Description The description of the control plane in Konnect.
	Description *string `json:"description,omitempty"`

	// Labels Labels to facilitate tagged search on control planes. Keys must be of length 1-63 characters, and cannot start with 'kong', 'konnect', 'mesh', 'kic', or '_'.
	Labels *Labels `json:"labels,omitempty"`

	// Name The name of the control plane.
	Name *string `json:"name,omitempty"`
}

// FilterByNameContains defines model for FilterByNameContains.
type FilterByNameContains = string

// FilterByNameEquality defines model for FilterByNameEquality.
type FilterByNameEquality = string

// FilterByNameEqualityShort defines model for FilterByNameEqualityShort.
type FilterByNameEqualityShort = string

// PageNumber defines model for PageNumber.
type PageNumber = int

// PageSize defines model for PageSize.
type PageSize = int

// CreateControlPlaneResponse The control plane object contains information about a Kong control plane.
type CreateControlPlaneResponse = ControlPlane

// ListControlPlanesResponse defines model for ListControlPlanesResponse.
type ListControlPlanesResponse struct {
	Data *[]ControlPlane `json:"data,omitempty"`

	// Meta Returns pagination information
	Meta *PaginatedMeta `json:"meta,omitempty"`
}

// RetrieveControlPlaneResponse The control plane object contains information about a Kong control plane.
type RetrieveControlPlaneResponse = ControlPlane

// UpdateControlPlaneResponse The control plane object contains information about a Kong control plane.
type UpdateControlPlaneResponse = ControlPlane

// ListControlPlanesParams defines parameters for ListControlPlanes.
type ListControlPlanesParams struct {
	// PageSize How many items to include in a page.
	PageSize *PageSize `form:"page[size],omitempty" json:"page[size],omitempty"`

	// PageNumber The specific page number in the collection results.
	PageNumber *PageNumber `form:"page[number],omitempty" json:"page[number],omitempty"`

	// FilterNameEq Filter by direct equality comparison of the name property with a supplied value.
	FilterNameEq *FilterByNameEquality `form:"filter[name][eq],omitempty" json:"filter[name][eq],omitempty"`

	// FilterName Filter by direct equality comparison (short-hand) of the name property with a supplied value.
	FilterName *FilterByNameEqualityShort `form:"filter[name],omitempty" json:"filter[name],omitempty"`

	// FilterNameContains Filter by contains comparison of the name property with a supplied substring
	FilterNameContains *FilterByNameContains `form:"filter[name][contains],omitempty" json:"filter[name][contains],omitempty"`
}

// CreateControlPlaneJSONRequestBody defines body for CreateControlPlane for application/json ContentType.
type CreateControlPlaneJSONRequestBody = CreateControlPlaneRequest

// UpdateControlPlaneJSONRequestBody defines body for UpdateControlPlane for application/json ContentType.
type UpdateControlPlaneJSONRequestBody = UpdateControlPlaneRequest

// Getter for additional properties for AdditionalErrorInformation_Item. Returns the specified
// element and whether it was found
func (a AdditionalErrorInformation_Item) Get(fieldName string) (value interface{}, found bool) {
	if a.AdditionalProperties != nil {
		value, found = a.AdditionalProperties[fieldName]
	}
	return
}

// Setter for additional properties for AdditionalErrorInformation_Item
func (a *AdditionalErrorInformation_Item) Set(fieldName string, value interface{}) {
	if a.AdditionalProperties == nil {
		a.AdditionalProperties = make(map[string]interface{})
	}
	a.AdditionalProperties[fieldName] = value
}

// Override default JSON handling for AdditionalErrorInformation_Item to handle AdditionalProperties
func (a *AdditionalErrorInformation_Item) UnmarshalJSON(b []byte) error {
	object := make(map[string]json.RawMessage)
	err := json.Unmarshal(b, &object)
	if err != nil {
		return err
	}

	if raw, found := object["field"]; found {
		err = json.Unmarshal(raw, &a.Field)
		if err != nil {
			return fmt.Errorf("error reading 'field': %w", err)
		}
		delete(object, "field")
	}

	if raw, found := object["reason"]; found {
		err = json.Unmarshal(raw, &a.Reason)
		if err != nil {
			return fmt.Errorf("error reading 'reason': %w", err)
		}
		delete(object, "reason")
	}

	if raw, found := object["rule"]; found {
		err = json.Unmarshal(raw, &a.Rule)
		if err != nil {
			return fmt.Errorf("error reading 'rule': %w", err)
		}
		delete(object, "rule")
	}

	if len(object) != 0 {
		a.AdditionalProperties = make(map[string]interface{})
		for fieldName, fieldBuf := range object {
			var fieldVal interface{}
			err := json.Unmarshal(fieldBuf, &fieldVal)
			if err != nil {
				return fmt.Errorf("error unmarshalling field %s: %w", fieldName, err)
			}
			a.AdditionalProperties[fieldName] = fieldVal
		}
	}
	return nil
}

// Override default JSON handling for AdditionalErrorInformation_Item to handle AdditionalProperties
func (a AdditionalErrorInformation_Item) MarshalJSON() ([]byte, error) {
	var err error
	object := make(map[string]json.RawMessage)

	if a.Field != nil {
		object["field"], err = json.Marshal(a.Field)
		if err != nil {
			return nil, fmt.Errorf("error marshaling 'field': %w", err)
		}
	}

	if a.Reason != nil {
		object["reason"], err = json.Marshal(a.Reason)
		if err != nil {
			return nil, fmt.Errorf("error marshaling 'reason': %w", err)
		}
	}

	if a.Rule != nil {
		object["rule"], err = json.Marshal(a.Rule)
		if err != nil {
			return nil, fmt.Errorf("error marshaling 'rule': %w", err)
		}
	}

	for fieldName, field := range a.AdditionalProperties {
		object[fieldName], err = json.Marshal(field)
		if err != nil {
			return nil, fmt.Errorf("error marshaling '%s': %w", fieldName, err)
		}
	}
	return json.Marshal(object)
}

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// ListControlPlanes request
	ListControlPlanes(ctx context.Context, params *ListControlPlanesParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateControlPlane request with any body
	CreateControlPlaneWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateControlPlane(ctx context.Context, body CreateControlPlaneJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteControlPlane request
	DeleteControlPlane(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetControlPlane request
	GetControlPlane(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateControlPlane request with any body
	UpdateControlPlaneWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateControlPlane(ctx context.Context, id openapi_types.UUID, body UpdateControlPlaneJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) ListControlPlanes(ctx context.Context, params *ListControlPlanesParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListControlPlanesRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateControlPlaneWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateControlPlaneRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateControlPlane(ctx context.Context, body CreateControlPlaneJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateControlPlaneRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteControlPlane(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteControlPlaneRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetControlPlane(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetControlPlaneRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateControlPlaneWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateControlPlaneRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateControlPlane(ctx context.Context, id openapi_types.UUID, body UpdateControlPlaneJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateControlPlaneRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewListControlPlanesRequest generates requests for ListControlPlanes
func NewListControlPlanesRequest(server string, params *ListControlPlanesParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/control-planes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	queryValues := queryURL.Query()

	if params.PageSize != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "page[size]", runtime.ParamLocationQuery, *params.PageSize); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	if params.PageNumber != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "page[number]", runtime.ParamLocationQuery, *params.PageNumber); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	if params.FilterNameEq != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "filter[name][eq]", runtime.ParamLocationQuery, *params.FilterNameEq); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	if params.FilterName != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "filter[name]", runtime.ParamLocationQuery, *params.FilterName); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	if params.FilterNameContains != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "filter[name][contains]", runtime.ParamLocationQuery, *params.FilterNameContains); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	queryURL.RawQuery = queryValues.Encode()

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewCreateControlPlaneRequest calls the generic CreateControlPlane builder with application/json body
func NewCreateControlPlaneRequest(server string, body CreateControlPlaneJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateControlPlaneRequestWithBody(server, "application/json", bodyReader)
}

// NewCreateControlPlaneRequestWithBody generates requests for CreateControlPlane with any type of body
func NewCreateControlPlaneRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/control-planes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteControlPlaneRequest generates requests for DeleteControlPlane
func NewDeleteControlPlaneRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/control-planes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetControlPlaneRequest generates requests for GetControlPlane
func NewGetControlPlaneRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/control-planes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateControlPlaneRequest calls the generic UpdateControlPlane builder with application/json body
func NewUpdateControlPlaneRequest(server string, id openapi_types.UUID, body UpdateControlPlaneJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateControlPlaneRequestWithBody(server, id, "application/json", bodyReader)
}

// NewUpdateControlPlaneRequestWithBody generates requests for UpdateControlPlane with any type of body
func NewUpdateControlPlaneRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/control-planes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// ListControlPlanes request
	ListControlPlanesWithResponse(ctx context.Context, params *ListControlPlanesParams, reqEditors ...RequestEditorFn) (*ListControlPlanesHTTPResponse, error)

	// CreateControlPlane request with any body
	CreateControlPlaneWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateControlPlaneHTTPResponse, error)

	CreateControlPlaneWithResponse(ctx context.Context, body CreateControlPlaneJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateControlPlaneHTTPResponse, error)

	// DeleteControlPlane request
	DeleteControlPlaneWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteControlPlaneHTTPResponse, error)

	// GetControlPlane request
	GetControlPlaneWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetControlPlaneHTTPResponse, error)

	// UpdateControlPlane request with any body
	UpdateControlPlaneWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateControlPlaneHTTPResponse, error)

	UpdateControlPlaneWithResponse(ctx context.Context, id openapi_types.UUID, body UpdateControlPlaneJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateControlPlaneHTTPResponse, error)
}

type ListControlPlanesHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Data *[]ControlPlane `json:"data,omitempty"`

		// Meta Returns pagination information
		Meta *PaginatedMeta `json:"meta,omitempty"`
	}
	JSON400 *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback ID.
		Instance *string `json:"instance,omitempty"`

		// InvalidParameters An array of objects that contains information related to the error response.
		InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

		// Status The HTTP response code
		Status Status400 `json:"status"`

		// Title The Error response
		Title string `json:"title"`
	}
	JSON401 *struct {
		// Detail Details about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status401 `json:"status"`

		// Title The Error Response.
		Title string `json:"title"`
	}
	JSON403 *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance Konnect traceback error code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status403 `json:"status"`

		// Title HTTP status code
		Title string `json:"title"`
	}
	JSON503 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status503 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
}

// Status returns HTTPResponse.Status
func (r ListControlPlanesHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListControlPlanesHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateControlPlaneHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *ControlPlane
	JSON400      *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback ID.
		Instance *string `json:"instance,omitempty"`

		// InvalidParameters An array of objects that contains information related to the error response.
		InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

		// Status The HTTP response code
		Status Status400 `json:"status"`

		// Title The Error response
		Title string `json:"title"`
	}
	JSON401 *struct {
		// Detail Details about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status401 `json:"status"`

		// Title The Error Response.
		Title string `json:"title"`
	}
	JSON403 *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance Konnect traceback error code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status403 `json:"status"`

		// Title HTTP status code
		Title string `json:"title"`
	}
	JSON409 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status409 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
	JSON500 *struct {
		// Details Details about the error.
		Details *string `json:"details,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status500 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
	JSON503 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status503 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
}

// Status returns HTTPResponse.Status
func (r CreateControlPlaneHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateControlPlaneHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteControlPlaneHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback ID.
		Instance *string `json:"instance,omitempty"`

		// InvalidParameters An array of objects that contains information related to the error response.
		InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

		// Status The HTTP response code
		Status Status400 `json:"status"`

		// Title The Error response
		Title string `json:"title"`
	}
	JSON401 *struct {
		// Detail Details about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status401 `json:"status"`

		// Title The Error Response.
		Title string `json:"title"`
	}
	JSON403 *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance Konnect traceback error code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status403 `json:"status"`

		// Title HTTP status code
		Title string `json:"title"`
	}
	JSON404 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status404 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
	JSON500 *struct {
		// Details Details about the error.
		Details *string `json:"details,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status500 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
	JSON503 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status503 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
}

// Status returns HTTPResponse.Status
func (r DeleteControlPlaneHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteControlPlaneHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetControlPlaneHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ControlPlane
	JSON400      *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback ID.
		Instance *string `json:"instance,omitempty"`

		// InvalidParameters An array of objects that contains information related to the error response.
		InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

		// Status The HTTP response code
		Status Status400 `json:"status"`

		// Title The Error response
		Title string `json:"title"`
	}
	JSON401 *struct {
		// Detail Details about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status401 `json:"status"`

		// Title The Error Response.
		Title string `json:"title"`
	}
	JSON403 *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance Konnect traceback error code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status403 `json:"status"`

		// Title HTTP status code
		Title string `json:"title"`
	}
	JSON404 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status404 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
	JSON503 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status503 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
}

// Status returns HTTPResponse.Status
func (r GetControlPlaneHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetControlPlaneHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateControlPlaneHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ControlPlane
	JSON400      *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback ID.
		Instance *string `json:"instance,omitempty"`

		// InvalidParameters An array of objects that contains information related to the error response.
		InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

		// Status The HTTP response code
		Status Status400 `json:"status"`

		// Title The Error response
		Title string `json:"title"`
	}
	JSON401 *struct {
		// Detail Details about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status401 `json:"status"`

		// Title The Error Response.
		Title string `json:"title"`
	}
	JSON403 *struct {
		// Detail Information about the error response.
		Detail *string `json:"detail,omitempty"`

		// Instance Konnect traceback error code.
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status403 `json:"status"`

		// Title HTTP status code
		Title string `json:"title"`
	}
	JSON404 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status404 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
	JSON500 *struct {
		// Details Details about the error.
		Details *string `json:"details,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status500 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
	JSON503 *struct {
		// Detail Details about the error.
		Detail *string `json:"detail,omitempty"`

		// Instance The Konnect traceback code
		Instance *string `json:"instance,omitempty"`

		// Status The HTTP status code.
		Status Status503 `json:"status"`

		// Title The error response code.
		Title string `json:"title"`
	}
}

// Status returns HTTPResponse.Status
func (r UpdateControlPlaneHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateControlPlaneHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// ListControlPlanesWithResponse request returning *ListControlPlanesHTTPResponse
func (c *ClientWithResponses) ListControlPlanesWithResponse(ctx context.Context, params *ListControlPlanesParams, reqEditors ...RequestEditorFn) (*ListControlPlanesHTTPResponse, error) {
	rsp, err := c.ListControlPlanes(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListControlPlanesHTTPResponse(rsp)
}

// CreateControlPlaneWithBodyWithResponse request with arbitrary body returning *CreateControlPlaneHTTPResponse
func (c *ClientWithResponses) CreateControlPlaneWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateControlPlaneHTTPResponse, error) {
	rsp, err := c.CreateControlPlaneWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateControlPlaneHTTPResponse(rsp)
}

func (c *ClientWithResponses) CreateControlPlaneWithResponse(ctx context.Context, body CreateControlPlaneJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateControlPlaneHTTPResponse, error) {
	rsp, err := c.CreateControlPlane(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateControlPlaneHTTPResponse(rsp)
}

// DeleteControlPlaneWithResponse request returning *DeleteControlPlaneHTTPResponse
func (c *ClientWithResponses) DeleteControlPlaneWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteControlPlaneHTTPResponse, error) {
	rsp, err := c.DeleteControlPlane(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteControlPlaneHTTPResponse(rsp)
}

// GetControlPlaneWithResponse request returning *GetControlPlaneHTTPResponse
func (c *ClientWithResponses) GetControlPlaneWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetControlPlaneHTTPResponse, error) {
	rsp, err := c.GetControlPlane(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetControlPlaneHTTPResponse(rsp)
}

// UpdateControlPlaneWithBodyWithResponse request with arbitrary body returning *UpdateControlPlaneHTTPResponse
func (c *ClientWithResponses) UpdateControlPlaneWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateControlPlaneHTTPResponse, error) {
	rsp, err := c.UpdateControlPlaneWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateControlPlaneHTTPResponse(rsp)
}

func (c *ClientWithResponses) UpdateControlPlaneWithResponse(ctx context.Context, id openapi_types.UUID, body UpdateControlPlaneJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateControlPlaneHTTPResponse, error) {
	rsp, err := c.UpdateControlPlane(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateControlPlaneHTTPResponse(rsp)
}

// ParseListControlPlanesHTTPResponse parses an HTTP response from a ListControlPlanesWithResponse call
func ParseListControlPlanesHTTPResponse(rsp *http.Response) (*ListControlPlanesHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListControlPlanesHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Data *[]ControlPlane `json:"data,omitempty"`

			// Meta Returns pagination information
			Meta *PaginatedMeta `json:"meta,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback ID.
			Instance *string `json:"instance,omitempty"`

			// InvalidParameters An array of objects that contains information related to the error response.
			InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

			// Status The HTTP response code
			Status Status400 `json:"status"`

			// Title The Error response
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest struct {
			// Detail Details about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status401 `json:"status"`

			// Title The Error Response.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 403:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance Konnect traceback error code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status403 `json:"status"`

			// Title HTTP status code
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON403 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 503:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status503 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON503 = &dest

	}

	return response, nil
}

// ParseCreateControlPlaneHTTPResponse parses an HTTP response from a CreateControlPlaneWithResponse call
func ParseCreateControlPlaneHTTPResponse(rsp *http.Response) (*CreateControlPlaneHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateControlPlaneHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest ControlPlane
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback ID.
			Instance *string `json:"instance,omitempty"`

			// InvalidParameters An array of objects that contains information related to the error response.
			InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

			// Status The HTTP response code
			Status Status400 `json:"status"`

			// Title The Error response
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest struct {
			// Detail Details about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status401 `json:"status"`

			// Title The Error Response.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 403:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance Konnect traceback error code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status403 `json:"status"`

			// Title HTTP status code
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON403 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status409 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest struct {
			// Details Details about the error.
			Details *string `json:"details,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status500 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 503:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status503 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON503 = &dest

	}

	return response, nil
}

// ParseDeleteControlPlaneHTTPResponse parses an HTTP response from a DeleteControlPlaneWithResponse call
func ParseDeleteControlPlaneHTTPResponse(rsp *http.Response) (*DeleteControlPlaneHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteControlPlaneHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback ID.
			Instance *string `json:"instance,omitempty"`

			// InvalidParameters An array of objects that contains information related to the error response.
			InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

			// Status The HTTP response code
			Status Status400 `json:"status"`

			// Title The Error response
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest struct {
			// Detail Details about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status401 `json:"status"`

			// Title The Error Response.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 403:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance Konnect traceback error code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status403 `json:"status"`

			// Title HTTP status code
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON403 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status404 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest struct {
			// Details Details about the error.
			Details *string `json:"details,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status500 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 503:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status503 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON503 = &dest

	}

	return response, nil
}

// ParseGetControlPlaneHTTPResponse parses an HTTP response from a GetControlPlaneWithResponse call
func ParseGetControlPlaneHTTPResponse(rsp *http.Response) (*GetControlPlaneHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetControlPlaneHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ControlPlane
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback ID.
			Instance *string `json:"instance,omitempty"`

			// InvalidParameters An array of objects that contains information related to the error response.
			InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

			// Status The HTTP response code
			Status Status400 `json:"status"`

			// Title The Error response
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest struct {
			// Detail Details about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status401 `json:"status"`

			// Title The Error Response.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 403:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance Konnect traceback error code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status403 `json:"status"`

			// Title HTTP status code
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON403 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status404 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 503:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status503 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON503 = &dest

	}

	return response, nil
}

// ParseUpdateControlPlaneHTTPResponse parses an HTTP response from a UpdateControlPlaneWithResponse call
func ParseUpdateControlPlaneHTTPResponse(rsp *http.Response) (*UpdateControlPlaneHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateControlPlaneHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ControlPlane
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback ID.
			Instance *string `json:"instance,omitempty"`

			// InvalidParameters An array of objects that contains information related to the error response.
			InvalidParameters *AdditionalErrorInformation `json:"invalid_parameters,omitempty"`

			// Status The HTTP response code
			Status Status400 `json:"status"`

			// Title The Error response
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest struct {
			// Detail Details about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status401 `json:"status"`

			// Title The Error Response.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 403:
		var dest struct {
			// Detail Information about the error response.
			Detail *string `json:"detail,omitempty"`

			// Instance Konnect traceback error code.
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status403 `json:"status"`

			// Title HTTP status code
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON403 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status404 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest struct {
			// Details Details about the error.
			Details *string `json:"details,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status500 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 503:
		var dest struct {
			// Detail Details about the error.
			Detail *string `json:"detail,omitempty"`

			// Instance The Konnect traceback code
			Instance *string `json:"instance,omitempty"`

			// Status The HTTP status code.
			Status Status503 `json:"status"`

			// Title The error response code.
			Title string `json:"title"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON503 = &dest

	}

	return response, nil
}
