// This code was initially generated by github.com/deepmap/oapi-codegen version v1.12.4. Later it was adapted manually
// to support github.com/oapi-codegen/runtime types.
//
// It's no longer possible to regenerate this from source therefore it's maintained manually
// until we have a proper Konnect API Go SDK: https://github.com/Kong/kubernetes-ingress-controller/issues/3550.

//nolint:all
package controlplanesconfig

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/oapi-codegen/runtime"
)

// PageNumber defines model for page_number.
type PageNumber = int

// PageSize defines model for page_size.
type PageSize = int

// GetDpClientCert defines model for get-dp-client-cert.
type GetDpClientCert struct {
	Item *struct {
		// Cert JSON escaped string of the certificate.
		Cert *string `json:"cert,omitempty"`

		// CreatedAt Date certificate was created.
		CreatedAt *int `json:"created_at,omitempty"`

		// Id Unique ID of the certificate entity.
		Id *string `json:"id,omitempty"`

		// UpdatedAt Date certificate was last updated.
		UpdatedAt *int `json:"updated_at,omitempty"`
	} `json:"item,omitempty"`
}

// GetExpectedConfigHash defines model for get-expected-config-hash.
type GetExpectedConfigHash struct {
	// CreatedAt Date the control plane configuration was created.
	CreatedAt *int `json:"created_at,omitempty"`

	// ExpectedHash The expected configuration hash.
	ExpectedHash *string `json:"expected_hash,omitempty"`

	// UpdatedAt Date the control plane configuration was last updated.
	UpdatedAt *int `json:"updated_at,omitempty"`
}

// GetNode defines model for get-node.
type GetNode struct {
	Item *struct {
		CompatibilityStatus *struct {
			State *string `json:"state,omitempty"`
		} `json:"compatibility_status,omitempty"`
		ConfigHash *string `json:"config_hash,omitempty"`
		CreatedAt  *int    `json:"created_at,omitempty"`
		Hostname   *string `json:"hostname,omitempty"`
		Id         *string `json:"id,omitempty"`
		LastPing   *int    `json:"last_ping,omitempty"`
		Type       *string `json:"type,omitempty"`
		UpdatedAt  *int    `json:"updated_at,omitempty"`
		Version    *string `json:"version,omitempty"`
	} `json:"item,omitempty"`
}

// ListDpClientCerts defines model for list-dp-client-certs.
type ListDpClientCerts struct {
	Items *[]struct {
		// Cert JSON escaped string of the certificate.
		Cert *string `json:"cert,omitempty"`

		// CreatedAt Date certificate was created.
		CreatedAt *int `json:"created_at,omitempty"`

		// Id Unique ID of the certificate entity.
		Id *string `json:"id,omitempty"`

		// UpdatedAt Date certificate was last updated.
		UpdatedAt *int `json:"updated_at,omitempty"`
	} `json:"items,omitempty"`
	Page *struct {
		TotalCount *int `json:"total_count,omitempty"`
	} `json:"page,omitempty"`
}

// ListNodes defines model for list-nodes.
type ListNodes struct {
	Items *[]struct {
		CompatibilityStatus *struct {
			State *string `json:"state,omitempty"`
		} `json:"compatibility_status,omitempty"`
		ConfigHash *string `json:"config_hash,omitempty"`
		CreatedAt  *int    `json:"created_at,omitempty"`
		Hostname   *string `json:"hostname,omitempty"`
		Id         *string `json:"id,omitempty"`
		LastPing   *int    `json:"last_ping,omitempty"`
		Type       *string `json:"type,omitempty"`
		UpdatedAt  *int    `json:"updated_at,omitempty"`
		Version    *string `json:"version,omitempty"`
	} `json:"items,omitempty"`
	Page *struct {
		TotalCount *int `json:"total_count,omitempty"`
	} `json:"page,omitempty"`
}

// CreateDpClientCert defines model for create-dp-client-cert.
type CreateDpClientCert struct {
	// Cert JSON escaped string of the certificate.
	Cert string `json:"cert"`
}

// GetDpClientCertificatesParams defines parameters for GetDpClientCertificates.
type GetDpClientCertificatesParams struct {
	// PageSize The number of items to include in a page.
	PageSize *PageSize `form:"page_size,omitempty" json:"page_size,omitempty"`

	// PageNumber The specific page number in the collection results.
	PageNumber *PageNumber `form:"page_number,omitempty" json:"page_number,omitempty"`
}

// PostDpClientCertificatesJSONBody defines parameters for PostDpClientCertificates.
type PostDpClientCertificatesJSONBody struct {
	// Cert JSON escaped string of the certificate.
	Cert string `json:"cert"`
}

// GetNodesParams defines parameters for GetNodes.
type GetNodesParams struct {
	// PageSize The number of items to include in a page.
	PageSize *PageSize `form:"page_size,omitempty" json:"page_size,omitempty"`

	// PageNumber The specific page number in the collection results.
	PageNumber *PageNumber `form:"page_number,omitempty" json:"page_number,omitempty"`
}

// PostDpClientCertificatesJSONRequestBody defines body for PostDpClientCertificates for application/json ContentType.
type PostDpClientCertificatesJSONRequestBody PostDpClientCertificatesJSONBody

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// DeleteCoreEntities request
	DeleteCoreEntities(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetCoreEntities request
	GetCoreEntities(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostCoreEntities request
	PostCoreEntities(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutCoreEntities request
	PutCoreEntities(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetDpClientCertificates request
	GetDpClientCertificates(ctx context.Context, params *GetDpClientCertificatesParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostDpClientCertificates request with any body
	PostDpClientCertificatesWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostDpClientCertificates(ctx context.Context, body PostDpClientCertificatesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteDpClientCertificatesCertId request
	DeleteDpClientCertificatesCertId(ctx context.Context, certificateId string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetDpClientCertificatesCertId request
	GetDpClientCertificatesCertId(ctx context.Context, certificateId string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetExpectedConfigHash request
	GetExpectedConfigHash(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetNodes request
	GetNodes(ctx context.Context, params *GetNodesParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteNodesNodeId request
	DeleteNodesNodeId(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetNodesNodeId request
	GetNodesNodeId(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) DeleteCoreEntities(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteCoreEntitiesRequest(c.Server, entityEndpoint)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetCoreEntities(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetCoreEntitiesRequest(c.Server, entityEndpoint)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostCoreEntities(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostCoreEntitiesRequest(c.Server, entityEndpoint)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutCoreEntities(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutCoreEntitiesRequest(c.Server, entityEndpoint)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetDpClientCertificates(ctx context.Context, params *GetDpClientCertificatesParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetDpClientCertificatesRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostDpClientCertificatesWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostDpClientCertificatesRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostDpClientCertificates(ctx context.Context, body PostDpClientCertificatesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostDpClientCertificatesRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteDpClientCertificatesCertId(ctx context.Context, certificateId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteDpClientCertificatesCertIdRequest(c.Server, certificateId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetDpClientCertificatesCertId(ctx context.Context, certificateId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetDpClientCertificatesCertIdRequest(c.Server, certificateId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetExpectedConfigHash(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetExpectedConfigHashRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetNodes(ctx context.Context, params *GetNodesParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetNodesRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteNodesNodeId(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteNodesNodeIdRequest(c.Server, nodeId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetNodesNodeId(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetNodesNodeIdRequest(c.Server, nodeId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewDeleteCoreEntitiesRequest generates requests for DeleteCoreEntities
func NewDeleteCoreEntitiesRequest(server string, entityEndpoint string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "entityEndpoint", runtime.ParamLocationPath, entityEndpoint)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/core-entities/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetCoreEntitiesRequest generates requests for GetCoreEntities
func NewGetCoreEntitiesRequest(server string, entityEndpoint string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "entityEndpoint", runtime.ParamLocationPath, entityEndpoint)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/core-entities/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostCoreEntitiesRequest generates requests for PostCoreEntities
func NewPostCoreEntitiesRequest(server string, entityEndpoint string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "entityEndpoint", runtime.ParamLocationPath, entityEndpoint)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/core-entities/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutCoreEntitiesRequest generates requests for PutCoreEntities
func NewPutCoreEntitiesRequest(server string, entityEndpoint string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "entityEndpoint", runtime.ParamLocationPath, entityEndpoint)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/core-entities/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetDpClientCertificatesRequest generates requests for GetDpClientCertificates
func NewGetDpClientCertificatesRequest(server string, params *GetDpClientCertificatesParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/dp-client-certificates")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	queryValues := queryURL.Query()

	if params.PageSize != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "page_size", runtime.ParamLocationQuery, *params.PageSize); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	if params.PageNumber != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "page_number", runtime.ParamLocationQuery, *params.PageNumber); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	queryURL.RawQuery = queryValues.Encode()

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostDpClientCertificatesRequest calls the generic PostDpClientCertificates builder with application/json body
func NewPostDpClientCertificatesRequest(server string, body PostDpClientCertificatesJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostDpClientCertificatesRequestWithBody(server, "application/json", bodyReader)
}

// NewPostDpClientCertificatesRequestWithBody generates requests for PostDpClientCertificates with any type of body
func NewPostDpClientCertificatesRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/dp-client-certificates")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteDpClientCertificatesCertIdRequest generates requests for DeleteDpClientCertificatesCertId
func NewDeleteDpClientCertificatesCertIdRequest(server string, certificateId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "certificateId", runtime.ParamLocationPath, certificateId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/dp-client-certificates/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetDpClientCertificatesCertIdRequest generates requests for GetDpClientCertificatesCertId
func NewGetDpClientCertificatesCertIdRequest(server string, certificateId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "certificateId", runtime.ParamLocationPath, certificateId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/dp-client-certificates/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetExpectedConfigHashRequest generates requests for GetExpectedConfigHash
func NewGetExpectedConfigHashRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/expected-config-hash")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetNodesRequest generates requests for GetNodes
func NewGetNodesRequest(server string, params *GetNodesParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nodes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	queryValues := queryURL.Query()

	if params.PageSize != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "page_size", runtime.ParamLocationQuery, *params.PageSize); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	if params.PageNumber != nil {

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "page_number", runtime.ParamLocationQuery, *params.PageNumber); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

	}

	queryURL.RawQuery = queryValues.Encode()

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteNodesNodeIdRequest generates requests for DeleteNodesNodeId
func NewDeleteNodesNodeIdRequest(server string, nodeId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "nodeId", runtime.ParamLocationPath, nodeId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nodes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetNodesNodeIdRequest generates requests for GetNodesNodeId
func NewGetNodesNodeIdRequest(server string, nodeId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "nodeId", runtime.ParamLocationPath, nodeId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/nodes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// DeleteCoreEntities request
	DeleteCoreEntitiesWithResponse(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*DeleteCoreEntitiesHTTPResponse, error)

	// GetCoreEntities request
	GetCoreEntitiesWithResponse(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*GetCoreEntitiesHTTPResponse, error)

	// PostCoreEntities request
	PostCoreEntitiesWithResponse(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*PostCoreEntitiesHTTPResponse, error)

	// PutCoreEntities request
	PutCoreEntitiesWithResponse(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*PutCoreEntitiesHTTPResponse, error)

	// GetDpClientCertificates request
	GetDpClientCertificatesWithResponse(ctx context.Context, params *GetDpClientCertificatesParams, reqEditors ...RequestEditorFn) (*GetDpClientCertificatesHTTPResponse, error)

	// PostDpClientCertificates request with any body
	PostDpClientCertificatesWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostDpClientCertificatesHTTPResponse, error)

	PostDpClientCertificatesWithResponse(ctx context.Context, body PostDpClientCertificatesJSONRequestBody, reqEditors ...RequestEditorFn) (*PostDpClientCertificatesHTTPResponse, error)

	// DeleteDpClientCertificatesCertId request
	DeleteDpClientCertificatesCertIdWithResponse(ctx context.Context, certificateId string, reqEditors ...RequestEditorFn) (*DeleteDpClientCertificatesCertIdHTTPResponse, error)

	// GetDpClientCertificatesCertId request
	GetDpClientCertificatesCertIdWithResponse(ctx context.Context, certificateId string, reqEditors ...RequestEditorFn) (*GetDpClientCertificatesCertIdHTTPResponse, error)

	// GetExpectedConfigHash request
	GetExpectedConfigHashWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetExpectedConfigHashHTTPResponse, error)

	// GetNodes request
	GetNodesWithResponse(ctx context.Context, params *GetNodesParams, reqEditors ...RequestEditorFn) (*GetNodesHTTPResponse, error)

	// DeleteNodesNodeId request
	DeleteNodesNodeIdWithResponse(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*DeleteNodesNodeIdHTTPResponse, error)

	// GetNodesNodeId request
	GetNodesNodeIdWithResponse(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*GetNodesNodeIdHTTPResponse, error)
}

type DeleteCoreEntitiesHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r DeleteCoreEntitiesHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteCoreEntitiesHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetCoreEntitiesHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r GetCoreEntitiesHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetCoreEntitiesHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostCoreEntitiesHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostCoreEntitiesHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostCoreEntitiesHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutCoreEntitiesHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PutCoreEntitiesHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutCoreEntitiesHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetDpClientCertificatesHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Items *[]struct {
			// Cert JSON escaped string of the certificate.
			Cert *string `json:"cert,omitempty"`

			// CreatedAt Date certificate was created.
			CreatedAt *int `json:"created_at,omitempty"`

			// Id Unique ID of the certificate entity.
			Id *string `json:"id,omitempty"`

			// UpdatedAt Date certificate was last updated.
			UpdatedAt *int `json:"updated_at,omitempty"`
		} `json:"items,omitempty"`
		Page *struct {
			TotalCount *int `json:"total_count,omitempty"`
		} `json:"page,omitempty"`
	}
}

// Status returns HTTPResponse.Status
func (r GetDpClientCertificatesHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetDpClientCertificatesHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostDpClientCertificatesHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *struct {
		Item *struct {
			// Cert JSON escaped string of the certificate.
			Cert *string `json:"cert,omitempty"`

			// CreatedAt Date certificate was created.
			CreatedAt *int `json:"created_at,omitempty"`

			// Id Unique ID of the certificate entity.
			Id *string `json:"id,omitempty"`

			// UpdatedAt Date certificate was last updated.
			UpdatedAt *int `json:"updated_at,omitempty"`
		} `json:"item,omitempty"`
	}
}

// Status returns HTTPResponse.Status
func (r PostDpClientCertificatesHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostDpClientCertificatesHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteDpClientCertificatesCertIdHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r DeleteDpClientCertificatesCertIdHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteDpClientCertificatesCertIdHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetDpClientCertificatesCertIdHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Item *struct {
			// Cert JSON escaped string of the certificate.
			Cert *string `json:"cert,omitempty"`

			// CreatedAt Date certificate was created.
			CreatedAt *int `json:"created_at,omitempty"`

			// Id Unique ID of the certificate entity.
			Id *string `json:"id,omitempty"`

			// UpdatedAt Date certificate was last updated.
			UpdatedAt *int `json:"updated_at,omitempty"`
		} `json:"item,omitempty"`
	}
}

// Status returns HTTPResponse.Status
func (r GetDpClientCertificatesCertIdHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetDpClientCertificatesCertIdHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetExpectedConfigHashHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		// CreatedAt Date the control plane configuration was created.
		CreatedAt *int `json:"created_at,omitempty"`

		// ExpectedHash The expected configuration hash.
		ExpectedHash *string `json:"expected_hash,omitempty"`

		// UpdatedAt Date the control plane configuration was last updated.
		UpdatedAt *int `json:"updated_at,omitempty"`
	}
}

// Status returns HTTPResponse.Status
func (r GetExpectedConfigHashHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetExpectedConfigHashHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetNodesHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Items *[]struct {
			CompatibilityStatus *struct {
				State *string `json:"state,omitempty"`
			} `json:"compatibility_status,omitempty"`
			ConfigHash *string `json:"config_hash,omitempty"`
			CreatedAt  *int    `json:"created_at,omitempty"`
			Hostname   *string `json:"hostname,omitempty"`
			Id         *string `json:"id,omitempty"`
			LastPing   *int    `json:"last_ping,omitempty"`
			Type       *string `json:"type,omitempty"`
			UpdatedAt  *int    `json:"updated_at,omitempty"`
			Version    *string `json:"version,omitempty"`
		} `json:"items,omitempty"`
		Page *struct {
			TotalCount *int `json:"total_count,omitempty"`
		} `json:"page,omitempty"`
	}
}

// Status returns HTTPResponse.Status
func (r GetNodesHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetNodesHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteNodesNodeIdHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r DeleteNodesNodeIdHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteNodesNodeIdHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetNodesNodeIdHTTPResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Item *struct {
			CompatibilityStatus *struct {
				State *string `json:"state,omitempty"`
			} `json:"compatibility_status,omitempty"`
			ConfigHash *string `json:"config_hash,omitempty"`
			CreatedAt  *int    `json:"created_at,omitempty"`
			Hostname   *string `json:"hostname,omitempty"`
			Id         *string `json:"id,omitempty"`
			LastPing   *int    `json:"last_ping,omitempty"`
			Type       *string `json:"type,omitempty"`
			UpdatedAt  *int    `json:"updated_at,omitempty"`
			Version    *string `json:"version,omitempty"`
		} `json:"item,omitempty"`
	}
}

// Status returns HTTPResponse.Status
func (r GetNodesNodeIdHTTPResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetNodesNodeIdHTTPResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// DeleteCoreEntitiesWithResponse request returning *DeleteCoreEntitiesHTTPResponse
func (c *ClientWithResponses) DeleteCoreEntitiesWithResponse(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*DeleteCoreEntitiesHTTPResponse, error) {
	rsp, err := c.DeleteCoreEntities(ctx, entityEndpoint, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteCoreEntitiesHTTPResponse(rsp)
}

// GetCoreEntitiesWithResponse request returning *GetCoreEntitiesHTTPResponse
func (c *ClientWithResponses) GetCoreEntitiesWithResponse(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*GetCoreEntitiesHTTPResponse, error) {
	rsp, err := c.GetCoreEntities(ctx, entityEndpoint, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetCoreEntitiesHTTPResponse(rsp)
}

// PostCoreEntitiesWithResponse request returning *PostCoreEntitiesHTTPResponse
func (c *ClientWithResponses) PostCoreEntitiesWithResponse(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*PostCoreEntitiesHTTPResponse, error) {
	rsp, err := c.PostCoreEntities(ctx, entityEndpoint, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostCoreEntitiesHTTPResponse(rsp)
}

// PutCoreEntitiesWithResponse request returning *PutCoreEntitiesHTTPResponse
func (c *ClientWithResponses) PutCoreEntitiesWithResponse(ctx context.Context, entityEndpoint string, reqEditors ...RequestEditorFn) (*PutCoreEntitiesHTTPResponse, error) {
	rsp, err := c.PutCoreEntities(ctx, entityEndpoint, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutCoreEntitiesHTTPResponse(rsp)
}

// GetDpClientCertificatesWithResponse request returning *GetDpClientCertificatesHTTPResponse
func (c *ClientWithResponses) GetDpClientCertificatesWithResponse(ctx context.Context, params *GetDpClientCertificatesParams, reqEditors ...RequestEditorFn) (*GetDpClientCertificatesHTTPResponse, error) {
	rsp, err := c.GetDpClientCertificates(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetDpClientCertificatesHTTPResponse(rsp)
}

// PostDpClientCertificatesWithBodyWithResponse request with arbitrary body returning *PostDpClientCertificatesHTTPResponse
func (c *ClientWithResponses) PostDpClientCertificatesWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostDpClientCertificatesHTTPResponse, error) {
	rsp, err := c.PostDpClientCertificatesWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostDpClientCertificatesHTTPResponse(rsp)
}

func (c *ClientWithResponses) PostDpClientCertificatesWithResponse(ctx context.Context, body PostDpClientCertificatesJSONRequestBody, reqEditors ...RequestEditorFn) (*PostDpClientCertificatesHTTPResponse, error) {
	rsp, err := c.PostDpClientCertificates(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostDpClientCertificatesHTTPResponse(rsp)
}

// DeleteDpClientCertificatesCertIdWithResponse request returning *DeleteDpClientCertificatesCertIdHTTPResponse
func (c *ClientWithResponses) DeleteDpClientCertificatesCertIdWithResponse(ctx context.Context, certificateId string, reqEditors ...RequestEditorFn) (*DeleteDpClientCertificatesCertIdHTTPResponse, error) {
	rsp, err := c.DeleteDpClientCertificatesCertId(ctx, certificateId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteDpClientCertificatesCertIdHTTPResponse(rsp)
}

// GetDpClientCertificatesCertIdWithResponse request returning *GetDpClientCertificatesCertIdHTTPResponse
func (c *ClientWithResponses) GetDpClientCertificatesCertIdWithResponse(ctx context.Context, certificateId string, reqEditors ...RequestEditorFn) (*GetDpClientCertificatesCertIdHTTPResponse, error) {
	rsp, err := c.GetDpClientCertificatesCertId(ctx, certificateId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetDpClientCertificatesCertIdHTTPResponse(rsp)
}

// GetExpectedConfigHashWithResponse request returning *GetExpectedConfigHashHTTPResponse
func (c *ClientWithResponses) GetExpectedConfigHashWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetExpectedConfigHashHTTPResponse, error) {
	rsp, err := c.GetExpectedConfigHash(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetExpectedConfigHashHTTPResponse(rsp)
}

// GetNodesWithResponse request returning *GetNodesHTTPResponse
func (c *ClientWithResponses) GetNodesWithResponse(ctx context.Context, params *GetNodesParams, reqEditors ...RequestEditorFn) (*GetNodesHTTPResponse, error) {
	rsp, err := c.GetNodes(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetNodesHTTPResponse(rsp)
}

// DeleteNodesNodeIdWithResponse request returning *DeleteNodesNodeIdHTTPResponse
func (c *ClientWithResponses) DeleteNodesNodeIdWithResponse(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*DeleteNodesNodeIdHTTPResponse, error) {
	rsp, err := c.DeleteNodesNodeId(ctx, nodeId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteNodesNodeIdHTTPResponse(rsp)
}

// GetNodesNodeIdWithResponse request returning *GetNodesNodeIdHTTPResponse
func (c *ClientWithResponses) GetNodesNodeIdWithResponse(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*GetNodesNodeIdHTTPResponse, error) {
	rsp, err := c.GetNodesNodeId(ctx, nodeId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetNodesNodeIdHTTPResponse(rsp)
}

// ParseDeleteCoreEntitiesHTTPResponse parses an HTTP response from a DeleteCoreEntitiesWithResponse call
func ParseDeleteCoreEntitiesHTTPResponse(rsp *http.Response) (*DeleteCoreEntitiesHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteCoreEntitiesHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetCoreEntitiesHTTPResponse parses an HTTP response from a GetCoreEntitiesWithResponse call
func ParseGetCoreEntitiesHTTPResponse(rsp *http.Response) (*GetCoreEntitiesHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetCoreEntitiesHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePostCoreEntitiesHTTPResponse parses an HTTP response from a PostCoreEntitiesWithResponse call
func ParsePostCoreEntitiesHTTPResponse(rsp *http.Response) (*PostCoreEntitiesHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostCoreEntitiesHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePutCoreEntitiesHTTPResponse parses an HTTP response from a PutCoreEntitiesWithResponse call
func ParsePutCoreEntitiesHTTPResponse(rsp *http.Response) (*PutCoreEntitiesHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutCoreEntitiesHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetDpClientCertificatesHTTPResponse parses an HTTP response from a GetDpClientCertificatesWithResponse call
func ParseGetDpClientCertificatesHTTPResponse(rsp *http.Response) (*GetDpClientCertificatesHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetDpClientCertificatesHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Items *[]struct {
				// Cert JSON escaped string of the certificate.
				Cert *string `json:"cert,omitempty"`

				// CreatedAt Date certificate was created.
				CreatedAt *int `json:"created_at,omitempty"`

				// Id Unique ID of the certificate entity.
				Id *string `json:"id,omitempty"`

				// UpdatedAt Date certificate was last updated.
				UpdatedAt *int `json:"updated_at,omitempty"`
			} `json:"items,omitempty"`
			Page *struct {
				TotalCount *int `json:"total_count,omitempty"`
			} `json:"page,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostDpClientCertificatesHTTPResponse parses an HTTP response from a PostDpClientCertificatesWithResponse call
func ParsePostDpClientCertificatesHTTPResponse(rsp *http.Response) (*PostDpClientCertificatesHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostDpClientCertificatesHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest struct {
			Item *struct {
				// Cert JSON escaped string of the certificate.
				Cert *string `json:"cert,omitempty"`

				// CreatedAt Date certificate was created.
				CreatedAt *int `json:"created_at,omitempty"`

				// Id Unique ID of the certificate entity.
				Id *string `json:"id,omitempty"`

				// UpdatedAt Date certificate was last updated.
				UpdatedAt *int `json:"updated_at,omitempty"`
			} `json:"item,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseDeleteDpClientCertificatesCertIdHTTPResponse parses an HTTP response from a DeleteDpClientCertificatesCertIdWithResponse call
func ParseDeleteDpClientCertificatesCertIdHTTPResponse(rsp *http.Response) (*DeleteDpClientCertificatesCertIdHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteDpClientCertificatesCertIdHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetDpClientCertificatesCertIdHTTPResponse parses an HTTP response from a GetDpClientCertificatesCertIdWithResponse call
func ParseGetDpClientCertificatesCertIdHTTPResponse(rsp *http.Response) (*GetDpClientCertificatesCertIdHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetDpClientCertificatesCertIdHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Item *struct {
				// Cert JSON escaped string of the certificate.
				Cert *string `json:"cert,omitempty"`

				// CreatedAt Date certificate was created.
				CreatedAt *int `json:"created_at,omitempty"`

				// Id Unique ID of the certificate entity.
				Id *string `json:"id,omitempty"`

				// UpdatedAt Date certificate was last updated.
				UpdatedAt *int `json:"updated_at,omitempty"`
			} `json:"item,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetExpectedConfigHashHTTPResponse parses an HTTP response from a GetExpectedConfigHashWithResponse call
func ParseGetExpectedConfigHashHTTPResponse(rsp *http.Response) (*GetExpectedConfigHashHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetExpectedConfigHashHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			// CreatedAt Date the control plane configuration was created.
			CreatedAt *int `json:"created_at,omitempty"`

			// ExpectedHash The expected configuration hash.
			ExpectedHash *string `json:"expected_hash,omitempty"`

			// UpdatedAt Date the control plane configuration was last updated.
			UpdatedAt *int `json:"updated_at,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetNodesHTTPResponse parses an HTTP response from a GetNodesWithResponse call
func ParseGetNodesHTTPResponse(rsp *http.Response) (*GetNodesHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetNodesHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Items *[]struct {
				CompatibilityStatus *struct {
					State *string `json:"state,omitempty"`
				} `json:"compatibility_status,omitempty"`
				ConfigHash *string `json:"config_hash,omitempty"`
				CreatedAt  *int    `json:"created_at,omitempty"`
				Hostname   *string `json:"hostname,omitempty"`
				Id         *string `json:"id,omitempty"`
				LastPing   *int    `json:"last_ping,omitempty"`
				Type       *string `json:"type,omitempty"`
				UpdatedAt  *int    `json:"updated_at,omitempty"`
				Version    *string `json:"version,omitempty"`
			} `json:"items,omitempty"`
			Page *struct {
				TotalCount *int `json:"total_count,omitempty"`
			} `json:"page,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseDeleteNodesNodeIdHTTPResponse parses an HTTP response from a DeleteNodesNodeIdWithResponse call
func ParseDeleteNodesNodeIdHTTPResponse(rsp *http.Response) (*DeleteNodesNodeIdHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteNodesNodeIdHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetNodesNodeIdHTTPResponse parses an HTTP response from a GetNodesNodeIdWithResponse call
func ParseGetNodesNodeIdHTTPResponse(rsp *http.Response) (*GetNodesNodeIdHTTPResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetNodesNodeIdHTTPResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Item *struct {
				CompatibilityStatus *struct {
					State *string `json:"state,omitempty"`
				} `json:"compatibility_status,omitempty"`
				ConfigHash *string `json:"config_hash,omitempty"`
				CreatedAt  *int    `json:"created_at,omitempty"`
				Hostname   *string `json:"hostname,omitempty"`
				Id         *string `json:"id,omitempty"`
				LastPing   *int    `json:"last_ping,omitempty"`
				Type       *string `json:"type,omitempty"`
				UpdatedAt  *int    `json:"updated_at,omitempty"`
				Version    *string `json:"version,omitempty"`
			} `json:"item,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}
