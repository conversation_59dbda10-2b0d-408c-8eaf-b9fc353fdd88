/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	v1 "github.com/kong/kubernetes-ingress-controller/v3/pkg/apis/configuration/v1"
	scheme "github.com/kong/kubernetes-ingress-controller/v3/pkg/clientset/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// KongConsumersGetter has a method to return a KongConsumerInterface.
// A group's client should implement this interface.
type KongConsumersGetter interface {
	KongConsumers(namespace string) KongConsumerInterface
}

// KongConsumerInterface has methods to work with KongConsumer resources.
type KongConsumerInterface interface {
	Create(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.CreateOptions) (*v1.KongConsumer, error)
	Update(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.UpdateOptions) (*v1.KongConsumer, error)
	UpdateStatus(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.UpdateOptions) (*v1.KongConsumer, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.KongConsumer, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.KongConsumerList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.KongConsumer, err error)
	KongConsumerExpansion
}

// kongConsumers implements KongConsumerInterface
type kongConsumers struct {
	client rest.Interface
	ns     string
}

// newKongConsumers returns a KongConsumers
func newKongConsumers(c *ConfigurationV1Client, namespace string) *kongConsumers {
	return &kongConsumers{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the kongConsumer, and returns the corresponding kongConsumer object, and an error if there is any.
func (c *kongConsumers) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.KongConsumer, err error) {
	result = &v1.KongConsumer{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("kongconsumers").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of KongConsumers that match those selectors.
func (c *kongConsumers) List(ctx context.Context, opts metav1.ListOptions) (result *v1.KongConsumerList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.KongConsumerList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("kongconsumers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested kongConsumers.
func (c *kongConsumers) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("kongconsumers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a kongConsumer and creates it.  Returns the server's representation of the kongConsumer, and an error, if there is any.
func (c *kongConsumers) Create(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.CreateOptions) (result *v1.KongConsumer, err error) {
	result = &v1.KongConsumer{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("kongconsumers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(kongConsumer).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a kongConsumer and updates it. Returns the server's representation of the kongConsumer, and an error, if there is any.
func (c *kongConsumers) Update(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.UpdateOptions) (result *v1.KongConsumer, err error) {
	result = &v1.KongConsumer{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("kongconsumers").
		Name(kongConsumer.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(kongConsumer).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *kongConsumers) UpdateStatus(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.UpdateOptions) (result *v1.KongConsumer, err error) {
	result = &v1.KongConsumer{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("kongconsumers").
		Name(kongConsumer.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(kongConsumer).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the kongConsumer and deletes it. Returns an error if one occurs.
func (c *kongConsumers) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("kongconsumers").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *kongConsumers) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("kongconsumers").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched kongConsumer.
func (c *kongConsumers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.KongConsumer, err error) {
	result = &v1.KongConsumer{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("kongconsumers").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
