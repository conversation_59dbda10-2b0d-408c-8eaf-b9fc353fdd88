/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1 "github.com/kong/kubernetes-ingress-controller/v3/pkg/apis/configuration/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeKongConsumers implements KongConsumerInterface
type FakeKongConsumers struct {
	Fake *FakeConfigurationV1
	ns   string
}

var kongconsumersResource = v1.SchemeGroupVersion.WithResource("kongconsumers")

var kongconsumersKind = v1.SchemeGroupVersion.WithKind("KongConsumer")

// Get takes name of the kongConsumer, and returns the corresponding kongConsumer object, and an error if there is any.
func (c *FakeKongConsumers) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.KongConsumer, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(kongconsumersResource, c.ns, name), &v1.KongConsumer{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.KongConsumer), err
}

// List takes label and field selectors, and returns the list of KongConsumers that match those selectors.
func (c *FakeKongConsumers) List(ctx context.Context, opts metav1.ListOptions) (result *v1.KongConsumerList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(kongconsumersResource, kongconsumersKind, c.ns, opts), &v1.KongConsumerList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1.KongConsumerList{ListMeta: obj.(*v1.KongConsumerList).ListMeta}
	for _, item := range obj.(*v1.KongConsumerList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested kongConsumers.
func (c *FakeKongConsumers) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(kongconsumersResource, c.ns, opts))

}

// Create takes the representation of a kongConsumer and creates it.  Returns the server's representation of the kongConsumer, and an error, if there is any.
func (c *FakeKongConsumers) Create(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.CreateOptions) (result *v1.KongConsumer, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(kongconsumersResource, c.ns, kongConsumer), &v1.KongConsumer{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.KongConsumer), err
}

// Update takes the representation of a kongConsumer and updates it. Returns the server's representation of the kongConsumer, and an error, if there is any.
func (c *FakeKongConsumers) Update(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.UpdateOptions) (result *v1.KongConsumer, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(kongconsumersResource, c.ns, kongConsumer), &v1.KongConsumer{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.KongConsumer), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeKongConsumers) UpdateStatus(ctx context.Context, kongConsumer *v1.KongConsumer, opts metav1.UpdateOptions) (*v1.KongConsumer, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(kongconsumersResource, "status", c.ns, kongConsumer), &v1.KongConsumer{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.KongConsumer), err
}

// Delete takes name of the kongConsumer and deletes it. Returns an error if one occurs.
func (c *FakeKongConsumers) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(kongconsumersResource, c.ns, name, opts), &v1.KongConsumer{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeKongConsumers) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(kongconsumersResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1.KongConsumerList{})
	return err
}

// Patch applies the patch and returns the patched kongConsumer.
func (c *FakeKongConsumers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.KongConsumer, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(kongconsumersResource, c.ns, name, pt, data, subresources...), &v1.KongConsumer{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.KongConsumer), err
}
