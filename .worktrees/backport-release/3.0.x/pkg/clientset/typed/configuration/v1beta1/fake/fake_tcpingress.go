/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1beta1 "github.com/kong/kubernetes-ingress-controller/v3/pkg/apis/configuration/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeTCPIngresses implements TCPIngressInterface
type FakeTCPIngresses struct {
	Fake *FakeConfigurationV1beta1
	ns   string
}

var tcpingressesResource = v1beta1.SchemeGroupVersion.WithResource("tcpingresses")

var tcpingressesKind = v1beta1.SchemeGroupVersion.WithKind("TCPIngress")

// Get takes name of the tCPIngress, and returns the corresponding tCPIngress object, and an error if there is any.
func (c *FakeTCPIngresses) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.TCPIngress, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(tcpingressesResource, c.ns, name), &v1beta1.TCPIngress{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.TCPIngress), err
}

// List takes label and field selectors, and returns the list of TCPIngresses that match those selectors.
func (c *FakeTCPIngresses) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.TCPIngressList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(tcpingressesResource, tcpingressesKind, c.ns, opts), &v1beta1.TCPIngressList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1beta1.TCPIngressList{ListMeta: obj.(*v1beta1.TCPIngressList).ListMeta}
	for _, item := range obj.(*v1beta1.TCPIngressList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested tCPIngresses.
func (c *FakeTCPIngresses) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(tcpingressesResource, c.ns, opts))

}

// Create takes the representation of a tCPIngress and creates it.  Returns the server's representation of the tCPIngress, and an error, if there is any.
func (c *FakeTCPIngresses) Create(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.CreateOptions) (result *v1beta1.TCPIngress, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(tcpingressesResource, c.ns, tCPIngress), &v1beta1.TCPIngress{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.TCPIngress), err
}

// Update takes the representation of a tCPIngress and updates it. Returns the server's representation of the tCPIngress, and an error, if there is any.
func (c *FakeTCPIngresses) Update(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.UpdateOptions) (result *v1beta1.TCPIngress, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(tcpingressesResource, c.ns, tCPIngress), &v1beta1.TCPIngress{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.TCPIngress), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeTCPIngresses) UpdateStatus(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.UpdateOptions) (*v1beta1.TCPIngress, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(tcpingressesResource, "status", c.ns, tCPIngress), &v1beta1.TCPIngress{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.TCPIngress), err
}

// Delete takes name of the tCPIngress and deletes it. Returns an error if one occurs.
func (c *FakeTCPIngresses) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(tcpingressesResource, c.ns, name, opts), &v1beta1.TCPIngress{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeTCPIngresses) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(tcpingressesResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1beta1.TCPIngressList{})
	return err
}

// Patch applies the patch and returns the patched tCPIngress.
func (c *FakeTCPIngresses) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.TCPIngress, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(tcpingressesResource, c.ns, name, pt, data, subresources...), &v1beta1.TCPIngress{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.TCPIngress), err
}
