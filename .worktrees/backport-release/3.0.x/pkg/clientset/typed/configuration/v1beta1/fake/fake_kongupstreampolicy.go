/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1beta1 "github.com/kong/kubernetes-ingress-controller/v3/pkg/apis/configuration/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeKongUpstreamPolicies implements KongUpstreamPolicyInterface
type FakeKongUpstreamPolicies struct {
	Fake *FakeConfigurationV1beta1
	ns   string
}

var kongupstreampoliciesResource = v1beta1.SchemeGroupVersion.WithResource("kongupstreampolicies")

var kongupstreampoliciesKind = v1beta1.SchemeGroupVersion.WithKind("KongUpstreamPolicy")

// Get takes name of the kongUpstreamPolicy, and returns the corresponding kongUpstreamPolicy object, and an error if there is any.
func (c *FakeKongUpstreamPolicies) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.KongUpstreamPolicy, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(kongupstreampoliciesResource, c.ns, name), &v1beta1.KongUpstreamPolicy{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.KongUpstreamPolicy), err
}

// List takes label and field selectors, and returns the list of KongUpstreamPolicies that match those selectors.
func (c *FakeKongUpstreamPolicies) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.KongUpstreamPolicyList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(kongupstreampoliciesResource, kongupstreampoliciesKind, c.ns, opts), &v1beta1.KongUpstreamPolicyList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1beta1.KongUpstreamPolicyList{ListMeta: obj.(*v1beta1.KongUpstreamPolicyList).ListMeta}
	for _, item := range obj.(*v1beta1.KongUpstreamPolicyList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested kongUpstreamPolicies.
func (c *FakeKongUpstreamPolicies) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(kongupstreampoliciesResource, c.ns, opts))

}

// Create takes the representation of a kongUpstreamPolicy and creates it.  Returns the server's representation of the kongUpstreamPolicy, and an error, if there is any.
func (c *FakeKongUpstreamPolicies) Create(ctx context.Context, kongUpstreamPolicy *v1beta1.KongUpstreamPolicy, opts v1.CreateOptions) (result *v1beta1.KongUpstreamPolicy, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(kongupstreampoliciesResource, c.ns, kongUpstreamPolicy), &v1beta1.KongUpstreamPolicy{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.KongUpstreamPolicy), err
}

// Update takes the representation of a kongUpstreamPolicy and updates it. Returns the server's representation of the kongUpstreamPolicy, and an error, if there is any.
func (c *FakeKongUpstreamPolicies) Update(ctx context.Context, kongUpstreamPolicy *v1beta1.KongUpstreamPolicy, opts v1.UpdateOptions) (result *v1beta1.KongUpstreamPolicy, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(kongupstreampoliciesResource, c.ns, kongUpstreamPolicy), &v1beta1.KongUpstreamPolicy{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.KongUpstreamPolicy), err
}

// Delete takes name of the kongUpstreamPolicy and deletes it. Returns an error if one occurs.
func (c *FakeKongUpstreamPolicies) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(kongupstreampoliciesResource, c.ns, name, opts), &v1beta1.KongUpstreamPolicy{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeKongUpstreamPolicies) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(kongupstreampoliciesResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1beta1.KongUpstreamPolicyList{})
	return err
}

// Patch applies the patch and returns the patched kongUpstreamPolicy.
func (c *FakeKongUpstreamPolicies) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.KongUpstreamPolicy, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(kongupstreampoliciesResource, c.ns, name, pt, data, subresources...), &v1beta1.KongUpstreamPolicy{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.KongUpstreamPolicy), err
}
