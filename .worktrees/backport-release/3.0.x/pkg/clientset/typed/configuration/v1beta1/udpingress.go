/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	"context"
	"time"

	v1beta1 "github.com/kong/kubernetes-ingress-controller/v3/pkg/apis/configuration/v1beta1"
	scheme "github.com/kong/kubernetes-ingress-controller/v3/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// UDPIngressesGetter has a method to return a UDPIngressInterface.
// A group's client should implement this interface.
type UDPIngressesGetter interface {
	UDPIngresses(namespace string) UDPIngressInterface
}

// UDPIngressInterface has methods to work with UDPIngress resources.
type UDPIngressInterface interface {
	Create(ctx context.Context, uDPIngress *v1beta1.UDPIngress, opts v1.CreateOptions) (*v1beta1.UDPIngress, error)
	Update(ctx context.Context, uDPIngress *v1beta1.UDPIngress, opts v1.UpdateOptions) (*v1beta1.UDPIngress, error)
	UpdateStatus(ctx context.Context, uDPIngress *v1beta1.UDPIngress, opts v1.UpdateOptions) (*v1beta1.UDPIngress, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1beta1.UDPIngress, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1beta1.UDPIngressList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.UDPIngress, err error)
	UDPIngressExpansion
}

// uDPIngresses implements UDPIngressInterface
type uDPIngresses struct {
	client rest.Interface
	ns     string
}

// newUDPIngresses returns a UDPIngresses
func newUDPIngresses(c *ConfigurationV1beta1Client, namespace string) *uDPIngresses {
	return &uDPIngresses{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the uDPIngress, and returns the corresponding uDPIngress object, and an error if there is any.
func (c *uDPIngresses) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.UDPIngress, err error) {
	result = &v1beta1.UDPIngress{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("udpingresses").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of UDPIngresses that match those selectors.
func (c *uDPIngresses) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.UDPIngressList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1beta1.UDPIngressList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("udpingresses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested uDPIngresses.
func (c *uDPIngresses) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("udpingresses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a uDPIngress and creates it.  Returns the server's representation of the uDPIngress, and an error, if there is any.
func (c *uDPIngresses) Create(ctx context.Context, uDPIngress *v1beta1.UDPIngress, opts v1.CreateOptions) (result *v1beta1.UDPIngress, err error) {
	result = &v1beta1.UDPIngress{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("udpingresses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(uDPIngress).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a uDPIngress and updates it. Returns the server's representation of the uDPIngress, and an error, if there is any.
func (c *uDPIngresses) Update(ctx context.Context, uDPIngress *v1beta1.UDPIngress, opts v1.UpdateOptions) (result *v1beta1.UDPIngress, err error) {
	result = &v1beta1.UDPIngress{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("udpingresses").
		Name(uDPIngress.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(uDPIngress).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *uDPIngresses) UpdateStatus(ctx context.Context, uDPIngress *v1beta1.UDPIngress, opts v1.UpdateOptions) (result *v1beta1.UDPIngress, err error) {
	result = &v1beta1.UDPIngress{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("udpingresses").
		Name(uDPIngress.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(uDPIngress).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the uDPIngress and deletes it. Returns an error if one occurs.
func (c *uDPIngresses) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("udpingresses").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *uDPIngresses) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("udpingresses").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched uDPIngress.
func (c *uDPIngresses) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.UDPIngress, err error) {
	result = &v1beta1.UDPIngress{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("udpingresses").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
