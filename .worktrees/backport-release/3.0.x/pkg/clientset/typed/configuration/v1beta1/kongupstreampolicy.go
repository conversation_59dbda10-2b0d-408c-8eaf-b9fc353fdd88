/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	"context"
	"time"

	v1beta1 "github.com/kong/kubernetes-ingress-controller/v3/pkg/apis/configuration/v1beta1"
	scheme "github.com/kong/kubernetes-ingress-controller/v3/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// KongUpstreamPoliciesGetter has a method to return a KongUpstreamPolicyInterface.
// A group's client should implement this interface.
type KongUpstreamPoliciesGetter interface {
	KongUpstreamPolicies(namespace string) KongUpstreamPolicyInterface
}

// KongUpstreamPolicyInterface has methods to work with KongUpstreamPolicy resources.
type KongUpstreamPolicyInterface interface {
	Create(ctx context.Context, kongUpstreamPolicy *v1beta1.KongUpstreamPolicy, opts v1.CreateOptions) (*v1beta1.KongUpstreamPolicy, error)
	Update(ctx context.Context, kongUpstreamPolicy *v1beta1.KongUpstreamPolicy, opts v1.UpdateOptions) (*v1beta1.KongUpstreamPolicy, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1beta1.KongUpstreamPolicy, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1beta1.KongUpstreamPolicyList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.KongUpstreamPolicy, err error)
	KongUpstreamPolicyExpansion
}

// kongUpstreamPolicies implements KongUpstreamPolicyInterface
type kongUpstreamPolicies struct {
	client rest.Interface
	ns     string
}

// newKongUpstreamPolicies returns a KongUpstreamPolicies
func newKongUpstreamPolicies(c *ConfigurationV1beta1Client, namespace string) *kongUpstreamPolicies {
	return &kongUpstreamPolicies{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the kongUpstreamPolicy, and returns the corresponding kongUpstreamPolicy object, and an error if there is any.
func (c *kongUpstreamPolicies) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.KongUpstreamPolicy, err error) {
	result = &v1beta1.KongUpstreamPolicy{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("kongupstreampolicies").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of KongUpstreamPolicies that match those selectors.
func (c *kongUpstreamPolicies) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.KongUpstreamPolicyList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1beta1.KongUpstreamPolicyList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("kongupstreampolicies").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested kongUpstreamPolicies.
func (c *kongUpstreamPolicies) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("kongupstreampolicies").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a kongUpstreamPolicy and creates it.  Returns the server's representation of the kongUpstreamPolicy, and an error, if there is any.
func (c *kongUpstreamPolicies) Create(ctx context.Context, kongUpstreamPolicy *v1beta1.KongUpstreamPolicy, opts v1.CreateOptions) (result *v1beta1.KongUpstreamPolicy, err error) {
	result = &v1beta1.KongUpstreamPolicy{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("kongupstreampolicies").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(kongUpstreamPolicy).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a kongUpstreamPolicy and updates it. Returns the server's representation of the kongUpstreamPolicy, and an error, if there is any.
func (c *kongUpstreamPolicies) Update(ctx context.Context, kongUpstreamPolicy *v1beta1.KongUpstreamPolicy, opts v1.UpdateOptions) (result *v1beta1.KongUpstreamPolicy, err error) {
	result = &v1beta1.KongUpstreamPolicy{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("kongupstreampolicies").
		Name(kongUpstreamPolicy.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(kongUpstreamPolicy).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the kongUpstreamPolicy and deletes it. Returns an error if one occurs.
func (c *kongUpstreamPolicies) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("kongupstreampolicies").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *kongUpstreamPolicies) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("kongupstreampolicies").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched kongUpstreamPolicy.
func (c *kongUpstreamPolicies) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.KongUpstreamPolicy, err error) {
	result = &v1beta1.KongUpstreamPolicy{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("kongupstreampolicies").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
