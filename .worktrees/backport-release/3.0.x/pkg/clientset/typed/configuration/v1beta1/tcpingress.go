/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	"context"
	"time"

	v1beta1 "github.com/kong/kubernetes-ingress-controller/v3/pkg/apis/configuration/v1beta1"
	scheme "github.com/kong/kubernetes-ingress-controller/v3/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// TCPIngressesGetter has a method to return a TCPIngressInterface.
// A group's client should implement this interface.
type TCPIngressesGetter interface {
	TCPIngresses(namespace string) TCPIngressInterface
}

// TCPIngressInterface has methods to work with TCPIngress resources.
type TCPIngressInterface interface {
	Create(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.CreateOptions) (*v1beta1.TCPIngress, error)
	Update(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.UpdateOptions) (*v1beta1.TCPIngress, error)
	UpdateStatus(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.UpdateOptions) (*v1beta1.TCPIngress, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1beta1.TCPIngress, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1beta1.TCPIngressList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.TCPIngress, err error)
	TCPIngressExpansion
}

// tCPIngresses implements TCPIngressInterface
type tCPIngresses struct {
	client rest.Interface
	ns     string
}

// newTCPIngresses returns a TCPIngresses
func newTCPIngresses(c *ConfigurationV1beta1Client, namespace string) *tCPIngresses {
	return &tCPIngresses{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the tCPIngress, and returns the corresponding tCPIngress object, and an error if there is any.
func (c *tCPIngresses) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1beta1.TCPIngress, err error) {
	result = &v1beta1.TCPIngress{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("tcpingresses").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of TCPIngresses that match those selectors.
func (c *tCPIngresses) List(ctx context.Context, opts v1.ListOptions) (result *v1beta1.TCPIngressList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1beta1.TCPIngressList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("tcpingresses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested tCPIngresses.
func (c *tCPIngresses) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("tcpingresses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a tCPIngress and creates it.  Returns the server's representation of the tCPIngress, and an error, if there is any.
func (c *tCPIngresses) Create(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.CreateOptions) (result *v1beta1.TCPIngress, err error) {
	result = &v1beta1.TCPIngress{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("tcpingresses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(tCPIngress).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a tCPIngress and updates it. Returns the server's representation of the tCPIngress, and an error, if there is any.
func (c *tCPIngresses) Update(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.UpdateOptions) (result *v1beta1.TCPIngress, err error) {
	result = &v1beta1.TCPIngress{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("tcpingresses").
		Name(tCPIngress.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(tCPIngress).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *tCPIngresses) UpdateStatus(ctx context.Context, tCPIngress *v1beta1.TCPIngress, opts v1.UpdateOptions) (result *v1beta1.TCPIngress, err error) {
	result = &v1beta1.TCPIngress{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("tcpingresses").
		Name(tCPIngress.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(tCPIngress).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the tCPIngress and deletes it. Returns an error if one occurs.
func (c *tCPIngresses) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("tcpingresses").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *tCPIngresses) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("tcpingresses").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched tCPIngress.
func (c *tCPIngresses) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1beta1.TCPIngress, err error) {
	result = &v1beta1.TCPIngress{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("tcpingresses").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
