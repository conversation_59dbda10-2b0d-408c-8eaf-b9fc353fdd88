/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1alpha1 "github.com/kong/kubernetes-ingress-controller/v3/pkg/apis/configuration/v1alpha1"
	scheme "github.com/kong/kubernetes-ingress-controller/v3/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// IngressClassParametersesGetter has a method to return a IngressClassParametersInterface.
// A group's client should implement this interface.
type IngressClassParametersesGetter interface {
	IngressClassParameterses(namespace string) IngressClassParametersInterface
}

// IngressClassParametersInterface has methods to work with IngressClassParameters resources.
type IngressClassParametersInterface interface {
	Create(ctx context.Context, ingressClassParameters *v1alpha1.IngressClassParameters, opts v1.CreateOptions) (*v1alpha1.IngressClassParameters, error)
	Update(ctx context.Context, ingressClassParameters *v1alpha1.IngressClassParameters, opts v1.UpdateOptions) (*v1alpha1.IngressClassParameters, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.IngressClassParameters, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.IngressClassParametersList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.IngressClassParameters, err error)
	IngressClassParametersExpansion
}

// ingressClassParameterses implements IngressClassParametersInterface
type ingressClassParameterses struct {
	client rest.Interface
	ns     string
}

// newIngressClassParameterses returns a IngressClassParameterses
func newIngressClassParameterses(c *ConfigurationV1alpha1Client, namespace string) *ingressClassParameterses {
	return &ingressClassParameterses{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the ingressClassParameters, and returns the corresponding ingressClassParameters object, and an error if there is any.
func (c *ingressClassParameterses) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.IngressClassParameters, err error) {
	result = &v1alpha1.IngressClassParameters{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("ingressclassparameterses").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of IngressClassParameterses that match those selectors.
func (c *ingressClassParameterses) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.IngressClassParametersList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.IngressClassParametersList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("ingressclassparameterses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested ingressClassParameterses.
func (c *ingressClassParameterses) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("ingressclassparameterses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a ingressClassParameters and creates it.  Returns the server's representation of the ingressClassParameters, and an error, if there is any.
func (c *ingressClassParameterses) Create(ctx context.Context, ingressClassParameters *v1alpha1.IngressClassParameters, opts v1.CreateOptions) (result *v1alpha1.IngressClassParameters, err error) {
	result = &v1alpha1.IngressClassParameters{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("ingressclassparameterses").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(ingressClassParameters).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a ingressClassParameters and updates it. Returns the server's representation of the ingressClassParameters, and an error, if there is any.
func (c *ingressClassParameterses) Update(ctx context.Context, ingressClassParameters *v1alpha1.IngressClassParameters, opts v1.UpdateOptions) (result *v1alpha1.IngressClassParameters, err error) {
	result = &v1alpha1.IngressClassParameters{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("ingressclassparameterses").
		Name(ingressClassParameters.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(ingressClassParameters).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the ingressClassParameters and deletes it. Returns an error if one occurs.
func (c *ingressClassParameterses) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("ingressclassparameterses").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *ingressClassParameterses) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("ingressclassparameterses").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched ingressClassParameters.
func (c *ingressClassParameterses) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.IngressClassParameters, err error) {
	result = &v1alpha1.IngressClassParameters{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("ingressclassparameterses").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
